import { useColorMode } from "@chakra-ui/react";
import { useCallback, useEffect, useRef } from "react";
import { ClientSideModel, getRecordStoreByVendor } from "@/store/record";
import "@/style/global.css";
import "@/App.css";
import "@/style/default-theme.css";
import { initWeblogger, collectPV } from "@/utils/weblogger";
import { logger } from "@/utils/logger";

import { RouterProvider } from "react-router-dom";
import { router } from "@/router";
import { useInlineChatStore } from "@/store/inline-chat";
import eventBus from "@/utils/eventBus";
import { httpClient } from "@/http";
import { useChatListener } from "@/hooks/useChatListener";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useUserListener } from "@/hooks/useUserListener";
import { reportUserCopy } from "@/http/api/feedback";
import { useWorkspaceStore } from "@/store/workspace";
import useInlineChatListener from "@/hooks/useInlineChatListener";
import { Tips } from "./components/Tips";
import { useStore } from "zustand";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { useRichEditPanelMenuStore } from "./store/richEditorPanelMenu";
import { getThemeTypeByKindId, getThemeSupportById, Theme, ViewModel } from "shared";
import { useIdeEnv } from "./hooks/useIdeEnv";
import { getBodyContainer } from "./utils/dom";
import { useViewModelStore } from "@/store/viewModel";
import { terminal } from "./components/Terminal";
import Radar from "@ks-radar/radar";
import { useViewModel } from "./hooks/useViewModel";
import clsx from "clsx";
import { APP_NAME, CUSTOM_THEME } from "shared/lib/const";

import * as monaco from "monaco-editor-core";
import { getHighlighterInstance } from "./utils/highlighter";
import { shikiToMonaco } from "@shikijs/monaco";

/** 扩展来自 ext 注入的变量 */
declare global {
  interface Window {
    ide?: "vscode";
    vscMediaUrl: string;
    colorThemeName: "dark" | "light";
    webkit?: any;
    kwaipilotBridgeCall?: any;
    WKWebViewJavascriptBridge?: any;
    WKWVJBCallbacks?: any;
    bridge?: any;
    proxyUrl?: string;
    globalRadar?: Radar;
    __injectTheme__: Theme;
    /**
     * 标识是否在 IDE 环境中
     */
    __KWAIPILOT_ENV__IN_IDE__: boolean;
    /**
     * 标识当前主题类型 ColorScheme
     * "dark" | "light" | "hcDark" | "hcLight"
     */
    __KWAIPILOT_ENV__THEME_TYPE__: "dark" | "light" | "hcDark" | "hcLight";
    /**
     * 标记当前是存在于侧边栏还是标签页
     */
    __KWAIPILOT_VIEW_TYPE__: ViewModel;
    /**
     * 标记全屏模式未退出下的路由
     */
    __KWAIPILOT_RESTORE_ROUTE__: string;
    /**
     * 当前ide的 app名称
     */
    __KWAIPILOT_ENV__APP_NAME__: string;
  }
}

const App = () => {
  const { colorMode, setColorMode } = useColorMode();
  const isInit = useRef(true);
  const setQuote = useInlineChatStore(state => state.setQuote);
  const [ide] = useIdeEnv();
  const clearInlineChatHistory = useInlineChatStore(
    state => state.clearInlineChatHistory,
  );
  const chatRecordStore = getRecordStoreByVendor("chat");
  const composerRecordStore = getRecordStoreByVendor("composer");
  const setEditorConfig = useVsEditorConfig(state => state.setEditorConfig);
  const richEditPanelMenuStoreSetDocList = useRichEditPanelMenuStore(state => state.setDocList);
  const setActiveSession = useStore(
    chatRecordStore,
    state => state.setActiveSession,
  );
  const activeSession = useStore(
    chatRecordStore,
    state => state.activeSession,
  );
  const { isPanel } = useViewModel();

  // 初始化 viewModel store
  const initializeViewModel = useViewModelStore(state => state.initialize);

  // FIXME: 输入框组件统一之后放到输入框组件中
  const setRuleFiles = useRichEditPanelMenuStore(state => state.setRuleFiles);
  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI
      .rulesList()
      .subscribe((list) => {
        logger.info("rulesList received:", JSON.stringify(list));
        setRuleFiles(list);
      });
    return () => {
      subscription.unsubscribe();
    };
  }, [setRuleFiles]);
  useEffect(() => {
    kwaiPilotBridgeAPI.onInlineChat((data) => {
      if (data) {
        clearInlineChatHistory();
        setQuote(data);
        eventBus.emit("inlineChat");
      }
      else {
        setActiveSession({
          value: activeSession,
          updateLocalStorage: false,
          navigateToChat: true,
          updateHistory: false,
        });
      }
    });
  }, [activeSession, clearInlineChatHistory, setActiveSession, setQuote]);

  useUserListener();
  useChatListener();
  useInlineChatListener();
  useEffect(() => {
    // 初始化 viewModel store
    initializeViewModel();

    // 添加热更新状态保持逻辑
    if (import.meta.hot) {
      import.meta.hot.accept(() => {
        // 保持状态
        const chatStore = getRecordStoreByVendor("chat");
        const composerStore = getRecordStoreByVendor("composer");

        const chatState = chatStore.getState();
        const composerState = composerStore.getState();

        // 重新初始化后恢复状态
        setTimeout(() => {
          const newChatStore = getRecordStoreByVendor("chat");
          const newComposerStore = getRecordStoreByVendor("composer");

          newChatStore.setState(chatState);
          newComposerStore.setState(composerState);
        }, 0);
      });
    }

    kwaiPilotBridgeAPI.webviewBridgeReady();
    initWeblogger();

    window.setTimeout(async () => {
      let isWorkspace = false;
      try {
        isWorkspace = await Promise.race([
          kwaiPilotBridgeAPI.extensionComposer
            .$getWorkspaceFile()
            .then(res => Boolean(res)),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error("timeout")), 5000),
          ),
        ]);
      }
      catch (e) {
        isWorkspace = false;
      }
      collectPV("VS_HOME", {
        source: "html",
        isWorkspace,
      });
    }, 500);
    const handleCopy = () => {
      if (navigator.clipboard) {
        navigator.clipboard
          .readText()
          .then((text) => {
            logger.info("copy content", "App.tsx", {
              value: text,
            });
            reportUserCopy(text);
          })
          .catch((err) => {
            logger.error("copy error", "App.tsx", {
              err,
            });
          });
      }
    };
    const copyWatchTarget = getBodyContainer();
    copyWatchTarget?.addEventListener("copy", handleCopy);
    return () => {
      copyWatchTarget?.removeEventListener("copy", handleCopy);
    };
  }, [chatRecordStore, composerRecordStore, initializeViewModel]);

  const setActiveProjectList = useWorkspaceStore(state => state.setAllRepos);

  const fetchModelList = useCallback(async () => {
    const AUTO_THINK_MODEL = "kwaipilot_40b";
    const modelList = await httpClient.getModelList();
    const finalModelList: ClientSideModel[] = modelList.map(model => ({
      ...model,
      isAutoThinkModel: model.modelType === AUTO_THINK_MODEL,
    }));
    chatRecordStore.getState().setModelList(finalModelList);
    composerRecordStore.getState().setModelList(finalModelList);
  }, [chatRecordStore, composerRecordStore]);
  const fetchDoclList = useCallback(async () => {
    const docList = await httpClient.getDocList();
    chatRecordStore.getState().setDocList(docList);
    composerRecordStore.getState().setDocList(docList);
    richEditPanelMenuStoreSetDocList(docList);
  }, [chatRecordStore, composerRecordStore, richEditPanelMenuStoreSetDocList]);
  const handleThemeId = useCallback(async () => {
    const body = getBodyContainer();
    if (!body) {
      return;
    }
    const themeId = body.getAttribute("data-vscode-theme-id");
    const themeKind = body.getAttribute("data-vscode-theme-kind") || "vscode-dark";
    if (!themeId) {
      return;
    }

    // 兜底三方主题
    const isSupportCurrentTheme = getThemeSupportById(themeId);
    const themeType = getThemeTypeByKindId(themeKind);
    if (!isSupportCurrentTheme) {
      body.classList.add("default-theme");
    }
    else {
      body.classList.remove("default-theme");
    }

    body.classList.remove("light", "dark");
    body.classList.add(themeType);

    const res = await kwaiPilotBridgeAPI.config.getThemeSettings(themeId, themeKind);
    if (res) {
      try {
        const h = getHighlighterInstance();
        // monaco中选中色是 selectionBackground 而不是 selectionHighlightBackground
        res.settings.colors["editor.selectionBackground"] = res.settings.colors["editor.selectionHighlightBackground"];
        await h.loadTheme({ ...res.settings, name: CUSTOM_THEME });
        shikiToMonaco(h, monaco);
        monaco.editor.setTheme(CUSTOM_THEME);
      }
      catch (error: any) {
        logger.error("set theme error", "App.tsx",
          error,
        );
      }
    }
  }, []);

  useEffect(() => {
    fetchModelList();
    fetchDoclList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const body = getBodyContainer();
    if (ide === "kwaipilot-xcode") {
      if (body?.classList.value.indexOf("default-theme") === -1) {
        body?.classList.add("default-theme");
      }
      return;
      /** 需要给body上添加kwaipilot-xcode 类 因为css变量挂在kwaipilot-xcode下面 */
    }
    if (ide === "kwaipilot-vscode" || ide === `${APP_NAME}-ide`) {
      if (body?.classList.value.indexOf("kwaipilot-vscode") === -1) {
        body?.classList.add("kwaipilot-vscode");
      }
    }
  }, [ide]);

  useEffect(() => {
    kwaiPilotBridgeAPI.extensionWebview.$getRestoreRoute().then((path) => {
      // 如果存在全屏模式下的路由，则使用该路由，否则使用默认路由
      const initialPath = path || "/composer-v2";
      // 设置路由的初始路径
      router.navigate(initialPath, { replace: true });
      // 记录当前路由
      logger.info(`initial path: ${initialPath}`, "App.tsx");
    });
    const unlisten = router.subscribe(({ location }) => {
      const currentPath = location.pathname;
      kwaiPilotBridgeAPI.extensionWebview.$setRestoreRoute(currentPath);
      // 记录当前路由
      logger.info(`current path: ${currentPath}`, "App.tsx");
    });
    return () => {
      unlisten();
    };
  }, []);

  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI
      .currentTheme()
      .subscribe((theme) => {
        if (theme !== colorMode) {
          setColorMode(theme);
        }
      });
    return () => {
      subscription.unsubscribe();
    };
  }, [colorMode, setColorMode]);
  // 监听主题变化
  useEffect(() => {
    kwaiPilotBridgeAPI.onActiveProjectChange(({ list }) => {
      setActiveProjectList(list);
    });
  }, [setActiveProjectList]);
  useEffect(() => {
    kwaiPilotBridgeAPI
      .getConfig<boolean>("enableSummaryConversation")
      .then((data) => {
        chatRecordStore.getState().setSummaryConversation(data.value);
        composerRecordStore.getState().setSummaryConversation(data.value);
      });
  }, [chatRecordStore, composerRecordStore]);
  useEffect(() => {
    kwaiPilotBridgeAPI.getAndWatchEditorConfig((editorConfig) => {
      setEditorConfig(editorConfig);
    });
  }, [setEditorConfig]);

  const goInlineChat = useCallback(() => {
    router.navigate("/inline-chat");
  }, []);

  useEffect(() => {
    eventBus.on("inlineChat", goInlineChat);
    return () => {
      eventBus.off("inlineChat", goInlineChat);
    };
  }, [goInlineChat]);
  useEffect(() => {
    isInit.current = false;
    const callback = async (mutationsList: MutationRecord[]) => {
      if (isInit.current) {
        return;
      }
      for (const mutation of mutationsList) {
        if (
          mutation.type === "attributes"
          && mutation.attributeName === "data-vscode-theme-id"
        ) {
          handleThemeId();
          terminal.freshTerminalTheme();
        }
      }
    };

    const observer = new MutationObserver(callback);
    const config = { attributes: true };
    const body = getBodyContainer();
    observer.observe(body, config);

    // 初始化时也执行一次，适配 IDE 场景
    handleThemeId();
    return () => {
      observer.disconnect();
    };
  }, [handleThemeId]);

  return (
    <div className="w-full h-screen bg-sideBar-background flex items-center justify-center">
      <div className={clsx("w-full h-screen flex flex-col", isPanel && "max-w-[800px]")}>
        <Tips />
        <div className="h-0 flex-1">
          <RouterProvider router={router} />
        </div>
      </div>
    </div>
  );
};

export default App;
