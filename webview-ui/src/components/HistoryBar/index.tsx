import { useNavigate, useSearchParams } from "react-router-dom";
import clsx from "clsx";
import { Popover, Tooltip } from "@/components/Union/chakra-ui";

import baseInfoManager from "@/utils/baseInfo";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useState, useEffect, useCallback } from "react";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { useViewModel } from "@/hooks/useViewModel";

import { useIdeEnv } from "@/hooks/useIdeEnv";
import { t } from "i18next";
import { SingleIcon } from "../SingleIcon";
import { Icon } from "../Union/t-iconify";
import { PopoverContent, PopoverTrigger, useToast } from "@chakra-ui/react";

import { vsCss } from "@/style/vscode";
import { Config } from "shared/lib/state-manager/types";
import { useHideOldAsk } from "@/hooks/useHideOldAsk";
import { reportUserAction } from "@/utils/weblogger";

function useDeveloperModeDisclosure(): {
  tap(): void;
  isDeveloperMode: boolean;
} {
  const MAX_TAP_DURATION = 500;
  const MAX_TAP_COUNT = 10;

  const toast = useToast();
  const [clickCount, setClickCount] = useState(0);
  const [lastClickTime, setLastClickTime] = useState(0);
  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode") ?? false;

  useEffect(() => {
    if (clickCount >= MAX_TAP_COUNT) {
      kwaiPilotBridgeAPI.extensionDeveloper.$setIsDeveloperMode(!isDeveloperMode)
        .then(() => {
          toast({
            title: isDeveloperMode ? t("nav.developerModeDisabled") : t("nav.developerModeEnabled"),
            status: "success",
          });
        });
      setClickCount(0);
    }
  }, [clickCount, isDeveloperMode, toast]);

  const tap = () => {
    const now = Date.now();
    if (now - lastClickTime > MAX_TAP_DURATION) {
      setClickCount(1);
    }
    else {
      setClickCount(prev => prev + 1);
    }
    setLastClickTime(now);
  };

  return {
    tap,
    isDeveloperMode,
  };
}

interface ViewModelChangeProps {
  shouldBlock?: () => boolean;
  onBlockedContinue?: () => void;
  hasIndeterminatedWorkingSet?: boolean;
  disabled?: boolean;
}

function ViewModelChange({ shouldBlock, onBlockedContinue, hasIndeterminatedWorkingSet, disabled }: ViewModelChangeProps) {
  const [fullScreenBlocked, setFullScreenBlocked] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  // 使用优化后的 useViewModel hook
  const { isPanel } = useViewModel();
  const navigate = useNavigate();

  const handleFullScreen = useCallback((newWindow: boolean) => {
    const action = () => kwaiPilotBridgeAPI.extensionWebview.$moveFullMode(newWindow);

    if (shouldBlock?.()) {
      setPendingAction(() => action);
      setFullScreenBlocked(true);
      return;
    }
    action();
  }, [shouldBlock]);

  const handleSiderbar = useCallback(() => {
    const action = () => kwaiPilotBridgeAPI.extensionWebview.$moveToSidebar();

    if (shouldBlock?.()) {
      setPendingAction(() => action);
      setFullScreenBlocked(true);
      return;
    }
    action();
  }, [shouldBlock]);

  const handleBlockedContinue = useCallback(() => {
    onBlockedContinue?.();
    if (pendingAction) {
      pendingAction();
      setPendingAction(null);
    }
    setFullScreenBlocked(false);
  }, [onBlockedContinue, pendingAction]);

  const handleBlockedCancel = useCallback(() => {
    setPendingAction(null);
    setFullScreenBlocked(false);
  }, []);

  return (
    <>
      {isPanel
        ? (
            <SingleIcon
              className="size-[20px]"
              onClick={handleSiderbar}
              title={disabled ? t("composer.disabledWhenGenerating") : t("viewModelChange.restoreToSidebar")}
              disabled={disabled}
            >
              <Icon icon="mynaui:arrow-down-right-square" />
            </SingleIcon>
          )
        : disabled
          ? (
              <Tooltip label={t("composer.disabledWhenGenerating")}>
                <div
                  className={clsx(
                    "w-[24px] h-[24px] rounded-[4px] flex items-center justify-center size-[20px] cursor-not-allowed opacity-50",
                  )}
                >
                  <Icon icon="codicon:ellipsis" />
                </div>
              </Tooltip>
            )
          : (
              <Popover
                placement="bottom-end"
                closeOnBlur={true}
              >
                <PopoverTrigger>
                  <div
                    className="w-[24px] h-[24px] rounded-[4px] flex items-center justify-center size-[20px] cursor-pointer hover:bg-toolbar-hoverBackground"
                  >
                    <Icon icon="codicon:ellipsis" />
                  </div>
                </PopoverTrigger>
                <PopoverContent w="120px" rounded="4px" p="2px" bg={vsCss.dropdownBackground} boxShadow="none">
                  <div
                    className="w-full rounded-sm px-[8px] py-[4px] relative hover:bg-list-dropBackground cursor-pointer"
                    onClick={() => handleFullScreen(false)}
                  >
                    {t("viewModelChange.openInEditor")}
                  </div>
                  <div
                    className="w-full rounded-sm px-[8px] py-[4px] relative hover:bg-list-dropBackground cursor-pointer"
                    onClick={() => handleFullScreen(true)}
                  >
                    {t("viewModelChange.openInNewWindow")}
                  </div>
                  {process.env.NODE_ENV === "development" && (
                    <div
                      className="w-full rounded-sm px-[8px] py-[4px] relative hover:bg-list-dropBackground cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation(); // 防止触发父级的 tap 事件
                        navigate("/dev-tools");
                      }}
                      title="开发工具 - 测试组件"
                    >
                      <Icon icon="codicon:tools" className="inline mr-1" />
                      Dev Tools
                    </div>
                  )}
                </PopoverContent>
              </Popover>
            )}
      {/* 阻断确认对话框 */}
      {fullScreenBlocked && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-editor-background border border-border-common rounded-lg p-4 max-w-md">
            <div className="flex items-center gap-2 mb-3">
              <Icon icon="codicon:warning" className="text-editorWarning-background" />
              <h3 className="text-text-common-primary font-medium text-[13px]">
                {hasIndeterminatedWorkingSet ? t("viewModelChange.unprocessedChanges") : t("viewModelChange.responseGenerating")}
              </h3>
            </div>
            <p className="text-text-common-secondary text-[13px] mb-4">
              {hasIndeterminatedWorkingSet
                ? t("viewModelChange.unprocessedChangesMessage")
                : t("viewModelChange.responseGeneratingMessage")}
            </p>
            <div className="flex justify-end gap-2">
              <button
                onClick={handleBlockedCancel}
                className="px-3 py-1 text-[12px] text-text-common-secondary bg-bg-scrollbar-default rounded hover:bg-list-dropBackground"
              >
                {t("viewModelChange.cancel")}
              </button>
              <button
                onClick={handleBlockedContinue}
                className="px-3 py-1 text-[12px] text-editor-background bg-text-common-primary rounded hover:opacity-80"
              >
                {t("viewModelChange.continue")}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export const HistoryBar = ({
  current = "chat",
  action,
  shouldBlockViewChange,
  onBlockedViewChangeContinue,
  hasIndeterminatedWorkingSet,
  isStreaming,
}: {
  // TODO: 根据路由自动匹配 current
  current?: "chat" | "history" | "composer" | "composer-v2";
  action?: React.ReactNode;
  shouldBlockViewChange?: () => boolean;
  onBlockedViewChangeContinue?: () => void;
  hasIndeterminatedWorkingSet?: boolean;
  /** 是否流式输出中 */
  isStreaming?: boolean;
}) => {
  const navigate = useNavigate();
  const { tap, isDeveloperMode } = useDeveloperModeDisclosure();
  const [,isKwaiPilotIDE] = useIdeEnv();
  const [isHideOldAsk] = useHideOldAsk();
  const [searchParams] = useSearchParams();
  const from = searchParams.get("from");
  // 是否是历史页面
  const isHistoryPage = current === "history";
  
  // 自定义数据库路径状态
  const [customDatabasePath, setCustomDatabasePath] = useState<string>("");

  // 监听配置变化
  useEffect(() => {
    // 初始获取配置
    const getInitialSettings = async () => {
      try {
        const settings = await kwaiPilotBridgeAPI.extensionSettings.$getSettings();
        const dbPath = settings[Config.CUSTOM_DATABASE_PATH];
        setCustomDatabasePath(typeof dbPath === 'string' ? dbPath : "");
      } catch (error) {
        console.warn("Failed to get initial custom database path:", error);
      }
    };
    
    getInitialSettings();

    // 监听配置变化
    const subscription = kwaiPilotBridgeAPI.observableAPI.settingUpdate().subscribe((update) => {
      if (update.key === Config.CUSTOM_DATABASE_PATH) {
        setCustomDatabasePath(typeof update.value === 'string' ? update.value : "");
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return (
    <div className="flex justify-between w-full h-9 items-center px-[12px] py-[7px] leading-[22px] text-[13px]" onClick={tap}>
      <div className="flex gap-[16px] items-center">
        {!baseInfoManager.isXcode && !isHistoryPage && (
          <div
            onClick={() => {
              reportUserAction({
                key: "compose_agent_show",
                type: undefined,
              });
              navigate("/composer-v2");
            }}
            className={clsx("hover:text-tab-activeForeground cursor-pointer relative",
              current === "composer-v2" || isHideOldAsk
                ? "text-tab-activeForeground"
                : "text-tab-inactiveForeground",
              current === "composer-v2" && !isHideOldAsk ? "after:content-[''] after:block after:absolute after:h-[2px] after:mt-[5px] after:left-1/2 after:transform after:-translate-x-1/2 after:bg-tab-activeForeground after:w-12 after:rounded-[12px_12px_2px_2px]" : "",
            )}
          >
            <div className="flex items-center gap-[2px]">
              <span className="group-hover:text-text-common-primary">{isHideOldAsk ? "Chat" : t("nav.agent")}</span>
            </div>
          </div>
        )}
        {
          !isHideOldAsk && !isHistoryPage && (
            <div
              onClick={() => {
                navigate("/chat");
              }}
              className={`hover:text-tab-activeForeground cursor-pointer relative ${
                current === "chat"
                  ? `text-tab-activeForeground after:content-[''] after:block after:absolute after:h-[2px] after:mt-[5px] after:left-1/2 after:transform after:-translate-x-1/2 after:w-7 after:rounded-[12px_12px_2px_2px] after:bg-tab-activeForeground`
                  : "text-tab-inactiveForeground"
              }`}
            >
              {t("nav.chat")}
            </div>
          )
        }
        {
          isHistoryPage && (
            <div
              className="text-[14px] flex items-center gap-1 cursor-pointer"
              onClick={() => {
                if (from === "chat" && !isKwaiPilotIDE) {
                  navigate("/chat");
                }
                else {
                  navigate("/composer-v2");
                }
              }}
            >
              <Icon icon="codicon:chevron-left" />
              <span className="text-icon-foreground opacity-50">{t("history.back")}</span>
            </div>
          )
        }
        {isDeveloperMode && (
          <>
            <div className="text-text-common-tertiary font-semibold text-xs italic select-none">{t("nav.developerMode")}</div>

          </>
        )}
        {customDatabasePath && (
          <>
            <div className="text-orange-400 font-semibold text-xs italic select-none">当前在mock其他用户的历史数据，请注意</div>
          </>
        )}
        
      </div>
      <div className="flex gap-2 items-center justify-center">
        {action}

        {
          isKwaiPilotIDE
            ? null
            : (
                <ViewModelChange
                  shouldBlock={shouldBlockViewChange}
                  onBlockedContinue={onBlockedViewChangeContinue}
                  hasIndeterminatedWorkingSet={hasIndeterminatedWorkingSet}
                  disabled={isStreaming && isHideOldAsk}
                />
              )
        }
      </div>
    </div>
  );
};
