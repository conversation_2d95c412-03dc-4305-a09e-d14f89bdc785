import { useCallback, useEffect, useMemo, useState, type ButtonHTMLAttributes } from "react";
import { Flex, Link as ChakraLink, Popover, PopoverBody, PopoverContent, PopoverTrigger, Text, Tooltip, chakra } from "@chakra-ui/react";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { t } from "i18next";
import { formatRelativeTime, formatAbsoluteTime } from "@/utils/time";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";
import { IconChevronDown, IconChevronUp, IconCopy, IconCycle, IconError, IconGlobe, IconInfo, IconLinkExternal, IconPass, IconRocket } from "./icons";
import { ReportKeys } from "@shared/types/logger";
// 本地定义该 UI 所需的部署事件 payload，避免改动 shared 公共类型
type VercelDeployEventPayload =
  | { kind: "auth_required" }
  | { kind: "no_workspace" }
  | { kind: "upload_start"; total: number }
  | { kind: "upload_progress"; uploaded: number; total: number }
  | { kind: "upload_done" }
  | { kind: "deployment_created"; url: string }
  | { kind: "phase"; phase: "UPLOADING" | "QUEUED" | "BUILDING" | "READY" | "ERROR" }
  | { kind: "result_success"; url: string; account?: string }
  | { kind: "result_error"; message?: string; errorLogs?: string; canceledAt?: number }
  | { kind: "account_info"; account: string }
  | { kind: "deployment_context"; id?: string; teamId?: string; projectId?: string; projectName?: string };

type Phase = "IDLE" | "NEED_AUTH" | "NO_WORKSPACE" | "UPLOADING" | "QUEUED" | "BUILDING" | "SUCCESS" | "ERROR";

function SecondaryButton(props: ButtonHTMLAttributes<HTMLButtonElement>) {
  const { className, children, style, ...rest } = props;
  return (
    <button
      {...rest}
      className={twMerge(clsx(
        "inline-flex items-center",
        "px-2 py-1 rounded border-none",
        "cursor-pointer transition-colors duration-200",
        "disabled disabled:cursor-not-allowed",
        "bg-[var(--vscode-button-secondaryBackground)]",
        "hover:bg-[var(--vscode-button-secondaryHoverBackground)]",
        "text-[var(--vscode-button-secondaryForeground)] text-sm",
        className,
      ))}
      style={{ fontFamily: "inherit", ...style }}
    >
      {children}
    </button>
  );
}

export function DeployPopover() {
  const [phase, setPhase] = useState<Phase>("IDLE");
  const [uploaded, setUploaded] = useState<number>(0);
  const [total, setTotal] = useState<number>(0);
  const [url, setUrl] = useState<string | undefined>(undefined);
  const [account, setAccount] = useState<string | undefined>(undefined);
  const [errorLogs, setErrorLogs] = useState<string | undefined>(undefined);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);
  const [isDeploying, setIsDeploying] = useState<boolean>(false);
  const [latestTeamId, setLatestTeamId] = useState<string | undefined>(undefined);
  const [latestTeamName, setLatestTeamName] = useState<string | undefined>(undefined);
  const [latestProjectName, setLatestProjectName] = useState<string | undefined>(undefined);
  const [latestDeploymentId, setLatestDeploymentId] = useState<string | undefined>(undefined);
  const [latestCreatedAt, setLatestCreatedAt] = useState<number | undefined>(undefined);
  const [latestCanceledAt, setLatestCanceledAt] = useState<number | undefined>(undefined);

  // 埋点上报
  const reportUserAction = useCallback((key: keyof ReportKeys, type?: string) => {
    try {
      kwaiPilotBridgeAPI.reportUserAction({ key, type });
    }
    catch (e) {
      console.warn("reportUserAction failed:", e);
    }
  }, []);

  useEffect(() => {
    const off = kwaiPilotBridgeAPI.onVercelDeployEvent((payload: VercelDeployEventPayload) => {
      switch (payload.kind) {
        case "deployment_context":
          setLatestDeploymentId((payload as any).id);
          setLatestTeamId((payload as any).teamId);
          setLatestTeamName((payload as any).teamName);
          setLatestProjectName((payload as any).projectName);
          setLatestCreatedAt((payload as any).createdAt);
          break;
        case "no_workspace":
          setPhase("NO_WORKSPACE");
          setIsDeploying(false);
          break;
        case "auth_required":
          setPhase("NEED_AUTH");
          setIsDeploying(false);
          break;
        case "account_info":
          setAccount(payload.account);
          break;
        case "upload_start":
          setTotal(payload.total ?? 0);
          setUploaded(0);
          setPhase("UPLOADING");
          break;
        case "upload_progress":
          setUploaded(payload.uploaded);
          setTotal(payload.total);
          setPhase("UPLOADING");
          break;
        case "upload_done":
          setPhase("QUEUED");
          break;
        case "deployment_created":
          setUrl(payload.url);
          break;
        case "phase":
          if (payload.phase === "READY") {
            setPhase("SUCCESS");
            setIsDeploying(false);
          }
          else if (payload.phase === "ERROR") {
            setPhase("ERROR");
            setIsDeploying(false);
          }
          else if (payload.phase === "QUEUED") {
            setPhase("QUEUED");
          }
          else if (payload.phase === "BUILDING") {
            setPhase("BUILDING");
          }
          else if (payload.phase === "UPLOADING") {
            setPhase("UPLOADING");
          }
          break;
        case "result_success":
          setUrl(payload.url);
          setAccount(payload.account);
          setPhase("SUCCESS");
          setIsDeploying(false);
          break;
        case "result_error":
          setErrorMessage(payload.message);
          setErrorLogs(payload.errorLogs);
          if ((payload as any)?.canceledAt) setLatestCanceledAt((payload as any).canceledAt);
          setPhase("ERROR");
          setIsDeploying(false);
          break;
      }
    });
    return () => off();
  }, []);

  // 面板渲染后尝试一次查询最近部署并展示（不改变 "IDLE" 初始文案，且不显示 0/0）
  // 每次弹窗打开时查询一次最新部署
  const [isOpen, setIsOpen] = useState(false);
  useEffect(() => {
    if (!isOpen) {
      // 关闭时通知后端停止轮询
      kwaiPilotBridgeAPI.vercelStopPolling();
      return;
    }
    (async () => {
      try {
        const res = await kwaiPilotBridgeAPI.vercelQueryLatest();
        if (res && res.ok) {
          if (res.url) setUrl(res.url);
          setLatestTeamId(prev => res.teamId || prev);
          setLatestTeamName(prev => (res as any).teamName || prev);
          setLatestProjectName(prev => res.projectName || prev);
          setLatestCreatedAt(prev => (res as any)?.raw?.createdAt || (res as any)?.raw?.created || prev);
          const rs = (res.readyState || "").toUpperCase();
          if (rs === "QUEUED") setPhase("QUEUED");
          else if (rs === "READY") setPhase("SUCCESS");
          else if (rs === "ERROR" || rs === "CANCELED" || rs === "CANCELLED") setPhase("ERROR");
          else if (rs === "BUILDING" || rs === "INITIALIZING" || rs === "DEPLOYING") setPhase("BUILDING");
        }
      }
      catch { /* ignore */ }
    })();
  }, [isOpen]);

  const statusText = useMemo(() => {
    switch (phase) {
      case "UPLOADING":
        return t("deploy.status.uploading");
      case "QUEUED":
        return t("deploy.status.queued");
      case "BUILDING":
        return t("deploy.status.building");
      case "SUCCESS":
        return t("deploy.status.success");
      case "ERROR":
        return t("deploy.status.error");
      default:
        return "";
    }
  }, [phase]);

  // 部署时间：相对时间 + 悬浮展示绝对时间
  const deploymentRelativeText = useMemo(() => formatRelativeTime(latestCreatedAt), [latestCreatedAt]);
  const deploymentAbsoluteText = useMemo(() => formatAbsoluteTime(latestCreatedAt), [latestCreatedAt]);
  const canceledRelativeText = useMemo(() => formatRelativeTime(latestCanceledAt), [latestCanceledAt]);
  const canceledAbsoluteText = useMemo(() => formatAbsoluteTime(latestCanceledAt), [latestCanceledAt]);

  const showDefaultIntro = phase === "IDLE";

  const handleOpenPopover = useCallback(() => {
    reportUserAction("deploy_popover_show");
    setIsOpen(true);
    // 打开时，如此前为未授权/无工作区，先恢复到默认态
    setPhase(prev => (prev === "NEED_AUTH" || prev === "NO_WORKSPACE") ? "IDLE" : prev);
    // 每次打开弹窗面板时，清空上次本地取消时间
    setLatestCanceledAt(undefined);
  }, [reportUserAction]);

  const handleClosePopover = useCallback(() => {
    setIsOpen(false);
  }, []);

  const handleNavigateUrl = useCallback(() => {
    if (!url) return;
    reportUserAction("deploy_popover_operation_click", "navigateUrl");
    kwaiPilotBridgeAPI.openUrl(url);
  }, [reportUserAction, url]);

  const handleCopyUrl = useCallback(() => {
    reportUserAction("deploy_popover_operation_click", "copyUrl");
    kwaiPilotBridgeAPI.copyToClipboard(url || "");
  }, [reportUserAction, url]);

  const handleNavigateUser = useCallback(() => {
    reportUserAction("deploy_popover_operation_click", "navigateUser");
    kwaiPilotBridgeAPI.openUrl("https://vercel.com/account");
  }, [reportUserAction]);

  const handleNavigateLog = useCallback(() => {
    const team = latestTeamName || latestTeamId;
    if (!team || !latestProjectName || !latestDeploymentId) return;
    reportUserAction("deploy_popover_operation_click", "navigateLog");
    kwaiPilotBridgeAPI.openUrl(`https://vercel.com/${team}/${latestProjectName}/${latestDeploymentId}?filter=errors`);
  }, [reportUserAction, latestTeamName, latestTeamId, latestProjectName, latestDeploymentId]);

  const resetState = useCallback(() => {
    setUrl(undefined);
    setErrorLogs(undefined);
    setErrorMessage(undefined);
    setUploaded(0);
    setTotal(0);
    setPhase("IDLE");
    setLatestCanceledAt(undefined);
  }, []);

  const handleAuthorize = useCallback(() => {
    reportUserAction("deploy_popover_operation_click", "auth");
    setIsOpen(false);
    kwaiPilotBridgeAPI.vercelOauth();
  }, [reportUserAction]);

  const handleUpdateService = useCallback(() => {
    reportUserAction("deploy_popover_operation_click", "update");
    resetState();
    setIsDeploying(true);
    kwaiPilotBridgeAPI.vercelDeploy();
  }, [reportUserAction, resetState]);

  const handleErrorSend = useCallback(() => {
    reportUserAction("deploy_popover_operation_click", "errorSend");
    kwaiPilotBridgeAPI.vercelTriggerDeployErrorSend(errorLogs || "Unknown error");
  }, [reportUserAction, errorLogs]);

  const handleRedeploy = useCallback(() => {
    reportUserAction("deploy_popover_operation_click", "retry");
    resetState();
    setIsDeploying(true);
    kwaiPilotBridgeAPI.vercelDeploy();
  }, [reportUserAction, resetState]);

  const handleStartDeploy = useCallback(() => {
    reportUserAction("deploy_popover_operation_click", "deploy");
    resetState();
    setIsDeploying(true);
    kwaiPilotBridgeAPI.vercelDeploy();
  }, [reportUserAction, resetState]);

  const handleCancel = useCallback(() => {
    reportUserAction("deploy_popover_operation_click", "cancel");
    kwaiPilotBridgeAPI.vercelCancel();
  }, [reportUserAction]);

  return (
    <Popover placement="bottom-start" closeOnBlur isOpen={isOpen} onOpen={handleOpenPopover} onClose={handleClosePopover}>
      <PopoverTrigger>
        <chakra.button
          className={twMerge(clsx(
            "h-[26px] flex items-center whitespace-nowrap",
            "text-[14px] px-[10px] rounded",
            "bg-button-background text-button-foreground",
            "hover:bg-button-hoverBackground",
          ))}
        >
          <span className="mr-1">{t("deploy.title")}</span>
          {isOpen ? <IconChevronUp /> : <IconChevronDown />}
        </chakra.button>
      </PopoverTrigger>
      <PopoverContent
        width="389px"
        style={{
          backgroundColor: "var(--vscode-editorHoverWidget-statusBarBackground)",
          border: "1px solid var(--vscode-widget-border)",
          color: "var(--vscode-descriptionForeground)",
          padding: "4px 0",
          minHeight: "126px",
        }}
      >
        <PopoverBody>
          <Text fontWeight={500} mb={2} color="var(--vscode-foreground)" fontSize="sm" className="flex items-center">
            <IconRocket className="mr-1" />
            {t("deploy.title")}
          </Text>

          {(phase === "NEED_AUTH" || phase === "NO_WORKSPACE")
            ? (
                <div className="flex items-start text-[var(--vscode-descriptionForeground)]">
                  <IconInfo className="mr-1" />
                  <Text fontSize="sm" className="flex-1">
                    {phase === "NEED_AUTH" ? t("deploy.intro.needAuth") : t("deploy.noWorkspace.tip")}
                  </Text>
                </div>
              )
            : (
                <Text fontSize="sm" className="text-[var(--vscode-descriptionForeground)]">
                  {t("deploy.intro.desc")}
                </Text>
              )}

          {
            phase !== "IDLE" && phase !== "NEED_AUTH" && phase !== "NO_WORKSPACE" && (
              <Flex direction="column" gap={2} className="rounded px-2 mt-3 bg-[var(--vscode-editor-background)]">
                <div className="my-3">
                  {url
                    ? (
                        <div className="pb-2 mb-2 border-b border-b-[var(--vscode-widget-border)] flex items-center">
                          <IconGlobe className="mr-1" />
                          <ChakraLink onClick={handleNavigateUrl} className="flex-1 min-w-0 overflow-hidden text-ellipsis whitespace-nowrap">
                            {url}
                          </ChakraLink>
                          <div className="ml-1 cursor-pointer" onClick={handleCopyUrl}>
                            <IconCopy />
                          </div>
                        </div>
                      )
                    : null}
                  <Flex justify="space-between" align="center">
                    <div className="flex items-center flex-shrink-0 min-w-0 mr-2">
                      {phase === "SUCCESS" ? (<IconPass className="mr-1" />) : null}
                      {phase === "ERROR" ? (<IconError className="mr-1" />) : null}
                      <Text fontSize="sm" color="var(--vscode-descriptionForeground)">{statusText}</Text>
                      {(phase === "SUCCESS" || phase === "ERROR") && (
                        phase === "ERROR" && latestCanceledAt
                          ? (
                              <Tooltip label={canceledAbsoluteText} hasArrow openDelay={300}>
                                <Text as="span" fontSize="sm" className="text-[var(--vscode-disabledForeground)] ml-1 max-w-[150px] overflow-hidden text-ellipsis whitespace-nowrap">{canceledRelativeText}</Text>
                              </Tooltip>
                            )
                          : latestCreatedAt
                            ? (
                                <Tooltip label={deploymentAbsoluteText} hasArrow openDelay={300}>
                                  <Text as="span" fontSize="sm" className="text-[var(--vscode-disabledForeground)] ml-1 max-w-[150px] overflow-hidden text-ellipsis whitespace-nowrap">{deploymentRelativeText}</Text>
                                </Tooltip>
                              )
                            : null
                      )}
                      {(phase === "UPLOADING" && total > 0)
                        ? (
                            <Text fontSize="sm" className="text-[var(--vscode-disabledForeground)] ml-1">
                              {uploaded}
                              /
                              {total}
                            </Text>
                          )
                        : null}
                    </div>
                    {!showDefaultIntro && (
                      <div className="ml-auto flex items-center min-w-0 max-w-[60%] justify-end">
                        <Text as="span" fontSize="xs" className="text-[var(--vscode-disabledForeground)] flex-shrink-0">
                          {t("deploy.from")}
                        </Text>
                        <ChakraLink onClick={handleNavigateUser} className="text-inherit ml-1 min-w-0 max-w-full flex items-center justify-end">
                          <Tooltip label={account || t("deploy.vercel")} hasArrow openDelay={300}>
                            <span className="text-[var(--vscode-textLink-foreground)] block overflow-hidden text-ellipsis whitespace-nowrap max-w-full">
                              {account ? `${account}` : t("deploy.vercel")}
                            </span>
                          </Tooltip>
                        </ChakraLink>
                      </div>
                    )}
                  </Flex>

                  {phase === "ERROR" && (errorMessage || errorLogs)
                    ? (
                        <Text fontSize="xs" whiteSpace="pre-wrap" className="mt-1 ml-5 max-h-36 overflow-auto text-[var(--vscode-disabledForeground)]">
                          {errorLogs || errorMessage}
                        </Text>
                      )
                    : null}
                </div>
              </Flex>
            )
          }
          <Flex justify="space-between" align="baseline" textAlign="right">

            {(phase === "ERROR" || phase === "SUCCESS") && (latestTeamName || latestTeamId) && latestProjectName && latestDeploymentId && (
              <ChakraLink
                className="text-[var(--vscode-descriptionForeground)]"
                onClick={handleNavigateLog}
              >
                <div className="flex items-center">
                  <span className="mr-1">{t("deploy.logs")}</span>
                  <IconLinkExternal />
                </div>
              </ChakraLink>
            )}
            {
              phase === "NEED_AUTH" && (
                <Text fontSize="xs" className="text-[var(--vscode-descriptionForeground)]">
                  {t("deploy.auth.tip.before")}
                  {" "}
                  <span className="text-[var(--vscode-textLink-foreground)]">{t("deploy.auth.allProjects")}</span>
                  {" "}
                  {t("deploy.auth.tip.after")}
                </Text>
              )
            }

            <Flex gap={2} className="ml-auto mt-3">
              {/* 权限态 */}
              {phase === "NEED_AUTH" && (
                <SecondaryButton onClick={handleAuthorize}>
                  {t("deploy.authorize")}
                </SecondaryButton>
              )}

              {/* 上传/排队/构建中 或 本地 loading：展示旋转加载图标 + 文案 */}
              {(phase === "UPLOADING" || phase === "QUEUED" || phase === "BUILDING" || isDeploying) && (
                <>
                  <SecondaryButton onClick={handleCancel} disabled={phase === "BUILDING" && !latestDeploymentId}>
                    {t("common.cancel")}
                  </SecondaryButton>
                  <SecondaryButton disabled>
                    <svg width="14" height="14" viewBox="0 0 24 24" className="animate-spin mr-1" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="9" stroke="currentColor" strokeWidth="3" fill="none" opacity="0.2" />
                      <path d="M21 12a9 9 0 0 1-9 9" stroke="currentColor" strokeWidth="3" strokeLinecap="round" fill="none" />
                    </svg>
                    {t("deploy.inProgress")}
                  </SecondaryButton>
                </>
              )}
              {/* 成功态：更新服务 */}
              {phase === "SUCCESS" && !isDeploying && (
                <SecondaryButton onClick={handleUpdateService}>
                  <IconCycle className="mr-1 text-[var(--vscode-button-secondaryForeground)]" />
                  {t("deploy.updateService")}
                </SecondaryButton>
              )}

              {/* 失败态：重新服务 + 错误修复 */}
              {phase === "ERROR" && !isDeploying && (
                <>
                  <SecondaryButton
                    onClick={handleErrorSend}
                    style={{
                      backgroundColor: "transparent",
                      border: "1px solid var(--vscode-editorWidget-border)",
                      color: "var(--vscode-foreground)",
                    }}
                  >
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ color: "var(--vscode-textLink-foreground)" }} className="mr-1">
                      <path d="M5.56479 10.0844C5.71879 10.1929 5.90342 10.2515 6.09154 10.2507C6.28054 10.2515 6.46517 10.192 6.61829 10.0818C6.77404 9.96803 6.89217 9.81053 6.95692 9.62853L7.34804 8.42715C7.44867 8.12615 7.61754 7.85228 7.84154 7.6274C8.06642 7.4034 8.33942 7.23365 8.64042 7.13303L9.85754 6.7384C9.99054 6.69115 10.1113 6.61328 10.2093 6.51178C10.3064 6.41028 10.3799 6.2869 10.4219 6.15215C10.4648 6.0174 10.4753 5.87478 10.4534 5.73565C10.4315 5.59565 10.3773 5.46353 10.295 5.34803C10.1778 5.18528 10.0107 5.0654 9.81904 5.0059L8.61592 4.61478C8.31492 4.51503 8.04104 4.34615 7.81617 4.12215C7.59129 3.89728 7.42154 3.62428 7.32092 3.3224L6.92542 2.1079C6.86242 1.93115 6.74604 1.77803 6.59117 1.6704C6.47567 1.58903 6.34179 1.53478 6.20092 1.51378C6.06092 1.4919 5.91742 1.50415 5.78179 1.5479C5.64704 1.59165 5.52367 1.6669 5.42304 1.76665C5.32154 1.8664 5.24542 1.9889 5.19904 2.12278L4.79917 3.34778C4.69942 3.6409 4.53404 3.90778 4.31529 4.1274C4.09742 4.34703 3.83229 4.51415 3.53917 4.61565L2.32204 5.00765C2.18992 5.05578 2.07004 5.13365 1.97204 5.23515C1.87404 5.33665 1.80142 5.45915 1.75854 5.5939C1.71654 5.72865 1.70604 5.87128 1.72792 6.0104C1.74979 6.14953 1.80317 6.28253 1.88454 6.39715C1.99654 6.55465 2.15492 6.67278 2.33692 6.7349L3.53917 7.12428C3.92504 7.2529 4.26279 7.49265 4.51042 7.81378C4.65304 7.9984 4.76242 8.20578 4.83504 8.42628L5.23054 9.6434C5.29354 9.8219 5.41079 9.9759 5.56479 10.0844ZM10.9347 13.6212C11.0537 13.706 11.1963 13.7507 11.3415 13.7507C11.4859 13.7507 11.6268 13.706 11.7449 13.6229C11.8674 13.5372 11.9593 13.4147 12.0074 13.2729L12.2244 12.6062C12.2717 12.4679 12.3495 12.3419 12.4519 12.2378C12.5552 12.1345 12.6812 12.0567 12.8194 12.0112L13.4949 11.7907C13.6332 11.7434 13.753 11.6533 13.837 11.5343C13.9018 11.4442 13.9438 11.34 13.9595 11.2307C13.9762 11.1213 13.9657 11.0102 13.9307 10.9052C13.8948 10.8002 13.8353 10.7048 13.7565 10.6278C13.6769 10.5508 13.5807 10.493 13.4748 10.4598L12.8063 10.2419C12.668 10.1964 12.542 10.1185 12.4388 10.0162C12.3355 9.9129 12.2577 9.7869 12.2113 9.64865L11.9908 8.97228C11.9444 8.8349 11.8543 8.71503 11.7344 8.63103C11.6452 8.56715 11.5428 8.52515 11.4343 8.50853C11.3258 8.4919 11.2147 8.50153 11.1105 8.53565C11.0055 8.56978 10.911 8.62753 10.8332 8.7054C10.7553 8.7824 10.6975 8.8769 10.6625 8.98103L10.4464 9.64778C10.4009 9.78603 10.3248 9.91203 10.2224 10.0153C10.1227 10.1177 10.0002 10.1955 9.86454 10.2419L9.18817 10.4624C9.04904 10.5097 8.92829 10.5998 8.84342 10.7197C8.75767 10.8387 8.71217 10.9822 8.71304 11.1292C8.71392 11.2762 8.76117 11.4197 8.84779 11.5378C8.93442 11.6568 9.05604 11.7452 9.19517 11.7907L9.86279 12.0068C10.0019 12.054 10.1288 12.1319 10.232 12.2352C10.3362 12.3393 10.4132 12.4653 10.4578 12.6044L10.6792 13.2817C10.7273 13.419 10.8165 13.5372 10.9347 13.6212Z" fill="currentColor" />
                    </svg>
                    {t("deploy.repair")}
                  </SecondaryButton>
                  <SecondaryButton onClick={handleRedeploy}>
                    <IconCycle className="mr-1 text-[var(--vscode-button-secondaryForeground)]" />
                    {t("deploy.redeploy")}
                  </SecondaryButton>
                </>
              )}

              {/* 初始态：发起部署（仅在非 loading 时显示） */}
              {(phase === "IDLE" || phase === "NO_WORKSPACE") && !isDeploying && (
                <SecondaryButton onClick={handleStartDeploy} disabled={phase === "NO_WORKSPACE"}>
                  {t("deploy.startDeploy")}
                </SecondaryButton>
              )}
            </Flex>
          </Flex>
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
}

export default DeployPopover;
