import { APP_NAME_PASCAL_CASE } from "shared/lib/const";

const DOCS_URL = process.env.VERSION_TYPE === "External" ? `${process.env.DEFAULT_PROXY_URL}/docs/feats/corefeat/rules.html` : "https://docs.corp.kuaishou.com/d/home/<USER>";

export default {
  // Login related
  "loginRequired": `${APP_NAME_PASCAL_CASE}: Please log in to use related features!`,
  "loginSuccessful": "Login successful!",
  "logoutSuccessful": "Logout successful!",
  "login": "Login",
  "dontRemindAgain": "Don't remind again",

  // Inline chat
  "failedToInitializeInlineChat": "Failed to initialize inline chat message processor",
  "conversationalCodeGeneration": "⌘+I Conversational Code Generation",
  "close": "Close",
  "inlineTip": "inline tip",
  "conversationalCodeGenerationTipDisabled": "Conversational code generation tip has been disabled.",

  // Code actions
  "functionComment": "Function Comment",
  "clickToGenerateFunctionComment": "Click to generate function comment",
  "lineComment": "Line Comment",
  "clickToGenerateLineComment": "Click to generate line comment",
  "codeExplanation": "Code Explanation",
  "clickToExplainCode": "Click to explain code",
  "functionSplit": "Function Split",
  "clickToSplitFunction": "Click to split function",
  "tuningSuggestion": "Tuning Suggestion",
  "clickToGetTuningSuggestions": "Click to get tuning suggestions",

  // Theme errors
  "colorThemeConfigError": "Color theme configuration error: 'colors' property is not an object",
  "errorLoadingThemeSettings": "Error loading theme settings",
  "errorParsingTheme": "Error parsing theme",

  // General errors
  "payloadRequired": "Payload is required",
  "failedToPassMessageToLocalService": "Failed to pass message to local-service: {{msg}}",
  "fileNotFound": "File not found: {{msg}}",

  // Rules
  "ruleConfigurationGuide": "Rule Configuration Guide",
  "ruleExampleDescription": "Example: 1. Add function-level comments when generating code. Note: This file should not exceed 5000 characters. Hover text can click \"Rule Configuration Guide\" to view detailed instructions.",
  "figmaRuleDescription": "Example: 1. Use Tailwind CSS for styling. 2. Components should have responsive design. 3. CSS properties need to pay attention to compatibility processing. Note: This rule is specifically for Figma design to code conversion. This file should not exceed 5000 characters.",
  "failedToUpdateRuleFileList": "Failed to update rule file list: {{msg}}",
  "failedToOpenUserRuleFile": "Failed to open user rule file: {{msg}}",
  "pleaseOpenProjectFirst": "Please open a project first",
  "onlyLowercaseSupported": "Only lowercase English letters, numbers, - and _ are supported",
  "maxFileNameLength": "Maximum file name length is 100",
  "pleaseEnterRuleFileName": "Please enter rule file name",
  "ruleConfigurationInstructions": `If this rule needs to be triggered in every conversation, please change 'false' to 'true' in the 'alwaysApply: false' above.\n  If this rule does not need to be committed with the project, please add this rule file to the .gitignore file.\n  View [Rule Configuration Guide](${DOCS_URL})`,
  "failedToOpenProjectRuleFile": "Failed to open project rule file: {{msg}}",

  // Annotation
  "currentSelectedCodeAnnotated": "Current selected code has been annotated, please select another code block",
  "selectedCodeBlockHasAnnotation": "Selected code block already has annotation, please reselect",

  // Settings
  "kwaipilotSetting": `${APP_NAME_PASCAL_CASE} Setting`,

  // Index file operations
  "confirmDeleteIndex": "Deleting the code index will affect the accuracy of repository code Q&A. Are you sure you want to delete the code index?",
  "confirmDeleteIndexButton": "Confirm",
  "noWorkspaceFolder": "No workspace folder found",
  "indexIgnoreFileContent": "# Add file directories or file types to ignore when building index (e.g. bin/a.js or *.csv), separated by line breaks;\n# If you only want to build specified directories like /src, you can use the combination of * and !/src",
  "notifyWebview": "Notify webview",
  "updatingIndex": "Updating index... {{progress}}%",
  "indexBuildPaused": "Index build paused...",

  // Wiki operations
  "updatingWiki": "Updating wiki... {{progress}}%",
  "updateWikiFailed": "Updating wiki failed",
  "updateWikiSuccess": "Updating wiki successfully",
  "openWikiFailed": "No wiki found",
  "confirmDeleteWiki": "Are you sure you want to delete the wiki?",
  "confirmDeleteWikiButton": "Confirm",

  // File upload operations
  "fileSizeExceedsLimit": "The total size of uploaded files exceeds the 3MB limit, please adjust and try again",
  "imageCountExceedsLimit": "Failed to add, up to 10 images can be added",

  // handleAddFileToContext
  "handleAddFileToContext.fileNotFound": "handleAddFileToContext: File not found",
  "handleAddFileToContext.notFileOrDirectory": "handleAddFileToContext: {{path}} is not a file/directory",
  "handleAddFileToContext.notFileOrDirectoryMessage": "{{path}} is not a file/directory",
  "handleAddFileToContext.failedToAdd": "Failed to add file/directory to agent context: {{path}}",

  // locateByPath
  "locateByPath.failedToLocate": "Unable to locate file {{path}}, please confirm the file exists",

  // locateMentionNodeV2
  "locateMentionNodeV2.failedToLocate": "Unable to locate file {{path}}, please confirm the file exists",
  "locateMentionNodeV2.failedToOpenImage": "Unable to open image file {{path}}, please confirm the file exists",

  // locateDiagnostic
  "locateDiagnostic.failedToLocate": "Unable to locate file {{path}}, please confirm the file exists",

  // executeCommand
  "executeCommand.commandFailed": "Command execution failed: {{error}}",

  // Log related messages
  "log.directoryNotExists": "Log directory does not exist",
  "log.cannotGeneratePackage": "Log directory does not exist, cannot generate log package",
  "log.generatingPackage": "Generating log package...",
  "log.filteringFiles": "Filtering log files...",
  "log.noFilesToPackage": "No log files found to package",
  "log.compressingFiles": "Compressing log files...",
  "log.verifyingPackage": "Verifying package...",
  "log.compressionComplete": "Compression complete, preparing for upload...",
  "log.uploadSuccess": "Log successfully uploaded, URL copied to clipboard: {{url}}",
  "log.uploadFailed": "Failed to generate log URL: {{message}}",
  "log.compressionHint": "\nHint: Log files might be too large or insufficient disk space",
  "log.uploadHint": "\nHint: Please check network connection and proxy settings",
  "log.timeoutHint": "\nHint: Operation timed out, please try again later",
  "log.compressionFailed": "Failed to create compression package",
  "log.uploadReturnNoUrl": "Upload successful but no URL returned",
  "log.compressionTimeout": "Compression operation timed out, please check log file size",
  "log.compressionTimeoutManual": "Compression operation timed out (60 seconds), please check log file size",
  "log.readLogDirectoryFailed": "Failed to read log directory",
  "log.packageTooLarge": "Package too large ({{sizeMB}}MB), please contact administrator",
  "log.uploadTimeout": "Upload timeout (5 minutes), please check network connection or file size",
  "log.uploadFailedWithFile": "Upload failed: {{filename}}{{error}}",

  // Log processing related log messages
  "log.message.filteredFiles": "Filtered log files",
  "log.message.readDirectoryFailed": "Failed to read log directory",
  "log.message.startCompression": "Starting log file compression",
  "log.message.compressionTimeout": "Compression timeout",
  "log.message.compressionFailed": "Log compression failed",
  "log.message.compressionComplete": "Log compression completed",
  "log.message.packageCreationSuccess": "Package creation successful",
  "log.message.startUpload": "Starting log package upload",
  "log.message.uploadStart": "Upload started",
  "log.message.uploadProgress": "Upload progress",
  "log.message.uploadSuccess": "Upload successful",
  "log.message.uploadFailed": "Upload failed",
  "log.message.urlGenerationSuccess": "Log URL generation successful",
  "log.message.urlGenerationFailed": "Log URL generation failed",
  "log.message.packageDeleted": "Local package deleted",
  "log.message.packageDeleteFailed": "Failed to delete local package",

  // Figma related
  "figmaTokenSaved": "Figma token saved successfully! You can copy the figma link in the input box to start exploring code generation.",
  "figmaTokenFailed": "Failed to save Figma token: {{msg}}",
  "figmaTokenUnknownError": "Unknown error",
  "figmaOAuthFailed": "Figma OAuth request failed: {{msg}}",
  "figmaOAuthPageOpened": "Figma OAuth page opened, please complete the authorization in the browser.",
  "figmaOAuthFailedToOpenPage": "Failed to open Figma OAuth page, please check the network connection.",
  "figmaTokenRefreshed": "Figma token refreshed successfully!",
  "figmaOAuthFailedToRefreshToken": "Failed to refresh Figma token: {{msg}}",
  "figmaOAuthFailedToRefreshTokenError": "Error refreshing Figma token: {{msg}}",

  // Preview 相关
  "error.previewPortInactive": "The port is inactive, please start the project first. Click the Preview button to start quickly.",

  // In-IDE Preview
  "in_ide_preview_panel_title": "Preview",
  "inIdePreview.urlPlaceholder": "please input url...",
  "inIdePreview.unavailableTitle": "Service unavailable",
  "inIdePreview.noServiceTitle": "No Web preview app available",
  "inIdePreview.noServiceDesc": "You can select a running service to preview, or ask Duet in chat to open it.",
  "inIdePreview.openPreviewFromComposer": "New Chat to Preview",
  "inIdePreview.openExternalTitle": `Open in ${APP_NAME_PASCAL_CASE} browser`,

  // mcp
  "mcp.messageBox.addMcpMessage": "MCP Server changed: {{serverName}} ",
  "mcp.messageBox.modifyMcpMessage": "New MCP Server detected: {{serverName}}",
  "mcp.messagebox.enable": "Enable",
  "mcp.messagebox.cancel": "Skip",
  // UI Preview 相关
  "inIdePreview.detectFrontendAtPort": "Detected frontend service at port {{port}}. Preview?",
  "inIdePreview.action.replace": "Confirm",
  "inIdePreview.action.cancel": "Cancel",
  // vercel 授权
  "vercelTokenSaved": "Vercel token saved successfully! You can deploy your project to Vercel now.",

  // Vercel 部署
  "vercel.deploy.noFilesToDeploy": "No files to deploy",
  "vercel.deploy.deployFailed": "Failed",
  "vercel.deploy.statusQueryFailed": "Status query failed",
  "vercel.deploy.unknownError": "Unknown error",
  "vercel.deploy.fileUploadFailed": "File upload failed: {{file}}, HTTP {{status}}",
  "vercel.deploy.canceled": "Deployment canceled",

  // 会话导入导出
  "kwaipilot.exportSession.placeholder": "Please enter the sessionId to export the session",
};
