export interface CodeSearchCheckoutRequest {
  repoName: string;
  commit: string;
  branch: string;
  username: string;
}

export interface GeneratePromptFile {
  code: string;
  language: string;
  name: string;
}

export interface CodeSearchGeneratePromptRequest {
  codebaseSearch: boolean;
  commit: string;
  files: GeneratePromptFile[];
  query: string;
  repoName: string;
  searchTargetDirs: string[];
  username: string;
}

export interface HttpClientResponse<T> {
  data: T;
  host: string;
  port: number;
  message: string;
  status: number;
  timestamp: string;
  traceId: string;
}

export interface PlatformConfigResponse {
  codeScreenShotConfig: {
    shotIntervalSeconds: number;
    shotSize: number;
    uploadCodeScreenShot: boolean;
  };
  promptConfigs: {
    key: string;
    name: string;
    template: string;
  }[];
  showTips: boolean;
  supportedPlatform: string[];
  supportedQaPlatform: {
    desc: string;
    multiValueList: string[];
    name: string;
    oncallTipMessage: string;
    value: string;
  }[];
  codePredictionConfig: {
    editHistorySize: number;
    editValidIntervalMs: number;
    blacklistMaxSize: number;
    blacklistExpireMs: number;
  };
}

export interface GetCodeSearchPromptResponse {
  prompt: string;
  list: [];
}

export interface GetCodeSearchCheckoutResponse {
  status: number;
}
export interface FetchEventSourceInit extends RequestInit {
  headers?: Record<string, string>;
  onopen?: (response: Response) => Promise<void>;
  onmessage?: (ev: EventSourceMessage) => void;
  onclose?: () => void;
  onerror?: (err: any) => number | null | undefined | void;
  openWhenHidden?: boolean;
  fetch?: typeof fetch;
}

export interface EventSourceMessage {
  id: string;
  event: string;
  data: string;
  retry?: number;
}

export interface GetPredictionResponse {
  diffBlocks: Prediction[];
}

export interface Prediction {
  diffBlockId: string;
  diffDeltas: PredictionDiffDelta[];
  sourceBlockContent: string;
  sourceStartOffset: number;
  sourceEndOffset: number;
  targetBlockContent: string;
  viewPosition: "after" | "inline";
}

export interface PredictionDiffDelta {
  // targetContent: {
  //   startOffset: number;
  //   endOffset: number;
  //   content: string;
  // };
  // SourceContent: {
  //   startOffset: number;
  //   endOffset: number;
  //   content: string;
  // }
  startOffset: number;
  endOffset: number;
  sourceContent: string;
  targetContent: string;
}

export interface AgentGrayResponse {
  enableLocalAgent: boolean;
}

export interface ReportEditFile {
  filepath: string;
  applyId: string;
  diffContent: DiffBlock[];
  diffLineCount: number;
  type: "single" | "all" | "block";
}

export interface DiffBlock {
  originStartLine: number;
  originEndLine: number;
  originContent: string;
  modifyStartLine: number;
  modifyEndLine: number;
  modifyContent: string;
}

export interface ModelConfig {
  type: string;
  name: string;
  desc: string;
  icon: string;
  supportImage: boolean;
  supportToolUse: boolean;
  supportThink: boolean;
  maxInputTokens: number;
}

export interface FigmaOAuthRequest {
  code?: string;
  redirect_uri?: string;
  grant_type: string;
  environment?: "inner" | "outer";
  refresh_token?: string;
}

export interface FigmaOAuthResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}
