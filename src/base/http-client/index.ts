import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import { ConfigManager, GlobalStateManager } from "../state-manager";
import { Config, GlobalState } from "../state-manager/types";
import { merge } from "lodash";
import {
  CodeSearchCheckoutRequest,
  CodeSearchGeneratePromptRequest,
  GetCodeSearchCheckoutResponse,
  GetCodeSearchPromptResponse, GetPredictionResponse, HttpClientResponse,
  PlatformConfigResponse, AgentGrayResponse,
  ReportEditFile,
  ModelConfig,
  FigmaOAuthRequest,
  FigmaOAuthResponse,
} from "./interface";
import { LoggerManager } from "../logger";
import { handleGitUrl } from "../../shared/utils/git";
import { fetchEventSource, FetchEventSourceInit } from "./fetch";
import { Doc, Model } from "../../shared/types/business";
import fetch from "node-fetch";
import {
  PLUGIN_PLATFORM,
} from "../../log/Model";
import { getProjectInfo } from "../../utils/projectInfo";
import { WebloggerManager } from "../weblogger";
import * as vscode from "vscode";
import { getAcceptLanguageHeader } from "shared/lib/util";
import { ExistsAnnotation } from "../../services/annotation-tag";
import { CompletionLogParams } from "../../core/completion/types";
import { APP_NAME } from "shared/lib/const";

interface HttpClientOptions {
  defaultBaseUrl: string;
  config?: fetch.RequestInit;
  requestInterceptors?: ((config: fetch.RequestInit) => fetch.RequestInit)[];
  responseInterceptors?: ((response: Response) => Response)[];
  tokenProvider?: () => Promise<string | undefined>;
}

export class HttpClient extends BaseModule {
  private _baseUrl: string = "";
  private defaultBaseUrl = "";
  private _config: fetch.RequestInit = {};
  private requestInterceptors: ((config: fetch.RequestInit) => fetch.RequestInit)[] = [];
  private responseInterceptors: ((response: any) => any)[] = [];
  public tokenProvider?: () => Promise<string | undefined>;

  constructor(ext: ContextManager, options: HttpClientOptions) {
    super(ext);
    this.defaultBaseUrl = options.defaultBaseUrl;
    this._config = options.config || {};
    this.requestInterceptors = options.requestInterceptors || [];
    this.responseInterceptors = options.responseInterceptors || [];
    this.tokenProvider = options.tokenProvider;

    this.getBase(ConfigManager).watch(
      Config.PROXY_URL,
      (nv?: string) => {
        this._baseUrl = nv || this.defaultBaseUrl;
        vscode.commands.executeCommand(`${APP_NAME}.bridge.updateProxyUrl`, this._baseUrl);
      },
      { immediate: true },
    );
  }

  async request<R>(url: string, requestInit?: fetch.RequestInit) {
    const response = await this.rawRequest(url, requestInit);

    let responseData = await response.json() as R;

    this.responseInterceptors.forEach((interceptor) => {
      responseData = interceptor(responseData);
    });

    this.logger.debug("api response after interceptor", "http-client", {
      value: responseData,
    });

    return responseData;
  }

  async rawRequest(url: string, requestInit?: fetch.RequestInit) {
    let config = this.mergeConfig(this._config, requestInit);

    // 动态获取 JWT token 并注入到请求头
    if (this.tokenProvider) {
      try {
        const token = await this.tokenProvider();
        if (token) {
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${token}`,
          };
          this.logger.debug("JWT token injected into request headers", "http-client");
        }
        else {
          this.logger.warn("Token provider returned empty token", "http-client");
        }
      }
      catch (error: any) {
        this.logger.error("Failed to get JWT token from provider", "http-client", { err: error });
      }
    }

    // 添加多语言支持 - 从 vscode.env.language 获取当前语言
    const currentLanguage = this.getCurrentLanguage();
    const acceptLanguage = getAcceptLanguageHeader(currentLanguage);

    config.headers = {
      "Content-Type": "application/json",
      "Accept-Language": acceptLanguage,
      ...config.headers,
    };
    config.method = config.method || "GET";
    let response;
    const fullUrl = this.buildUrl(url, this._baseUrl, "/api/proxy");
    this.logger.debug(`api request url: ${fullUrl}`, "http-client", {
      value: {
        url: fullUrl,
        config,
      },
    });
    this.requestInterceptors.forEach((interceptor) => {
      config = interceptor(config);
    });

    this.logger.debug("api request after interceptor", "http-client", {
      value: {
        url: fullUrl,
        config,
      },
    });
    try {
      response = await fetch(fullUrl, config);
    }
    catch (error: any) {
      if (error.type === "aborted") {
        throw "user abort";
      }
      this.logger.error(`api request error: ${config.method} ${new URL(fullUrl).pathname}`, "http-client", {
        value: {
          url: fullUrl,
          config,
        },
        err: error,
      });
      throw new Error(error.toString());
    }

    if (!response.ok) {
      this.logger.error(`api request error: ${config.method} ${new URL(fullUrl).pathname} ${response.status}`, "http-client", {
        value: {
          url: fullUrl,
          config,
          status: response.status,
          statusText: response.statusText,
        },
        reason: response.timeout ? "timeout" : "an error occurred",
      });
      throw new Error("An error occurred");
    }
    this.logger.info(`api request success: ${config.method} ${new URL(fullUrl).pathname} ${response.status}`, "http-client", {
      value: {
        url: fullUrl,
        config,
        response,
      },
    });
    return response;
  }

  async get<T, R = HttpClientResponse<T>>(url: string, params: Record<string, string> = {}, config: fetch.RequestInit = {}) {
    const p = this.formatUrl(url, params);
    config.method = "get";
    return this.request<R>(p, config);
  }

  async post<T, R = HttpClientResponse<T>, D extends fetch.BodyInit = any>(url: string, body?: D, config: fetch.RequestInit = {}) {
    config.method = "post";
    config.body = body;
    return this.request<R>(url, config);
  }

  async rawFetchEventSource(url: string, options: FetchEventSourceInit) {
    let fullUrl = null;
    if (typeof url === "string") {
      fullUrl = this.buildUrl(url, this._baseUrl, "/api/proxy/sse");
    }

    // 动态获取 JWT token 并注入到请求头
    if (this.tokenProvider) {
      try {
        const token = await this.tokenProvider();
        if (token) {
          options.headers = {
            ...options.headers,
            Authorization: `Bearer ${token}`,
          };
          this.logger.debug("JWT token injected into fetchEventSource headers", "http-client");
        }
        else {
          this.logger.warn("Token provider returned empty token for fetchEventSource", "http-client");
        }
      }
      catch (error: any) {
        this.logger.error("Failed to get JWT token from provider for fetchEventSource", "http-client", { err: error });
      }
    }

    // 添加多语言支持 - 从 vscode.env.language 获取当前语言
    const currentLanguage = this.getCurrentLanguage();
    const acceptLanguage = getAcceptLanguageHeader(currentLanguage);

    // 确保设置了默认的 Content-Type 和 Accept-Language
    options.headers = {
      "Content-Type": "application/json",
      "Accept-Language": acceptLanguage,
      ...options.headers,
    };

    this.logger.debug("fetch event source body", "http-client", {
      value: options.body,
    });

    this.logger.debug("fetch event source headers", "http-client", {
      value: options.headers,
    });

    return fetchEventSource(fullUrl || url, options);
  }

  /**
   * 获取当前语言设置
   * @returns 当前语言代码
   */
  private getCurrentLanguage(): string {
    return vscode.env.language.toLowerCase();
  }

  private mergeConfig(originConfig: fetch.RequestInit = {}, config: fetch.RequestInit = {}) {
    return merge({}, originConfig, config);
  }

  private formatUrl(url: string, params: Record<string, string>) {
    let isUrl = true;
    const urlObj = new URL(url, "http://example.com");

    try {
      new URL(url);
    }
    catch (error) {
      isUrl = false;
    }

    // 获取现有的搜索参数
    const searchParams = urlObj.searchParams;

    // 遍历新的参数对象，并添加到搜索参数中
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    if (!isUrl) {
      return `${urlObj.pathname}${urlObj.search}`;
    }

    return urlObj.toString();
  }

  /**
   * 构建完整的 URL，支持多种 baseUrl 格式
   * @param url 相对路径或完整 URL
   * @param baseUrl 基础 URL
   * @param defaultPath 可选的默认路径前缀，如果 baseUrl 不包含路径时使用
   * @returns 完整的 URL 字符串
   */
  private buildUrl(url: string, host: string, defaultPath?: string): string {
    // 如果 url 已经是完整 URL，直接返回
    try {
      new URL(url);
      return url;
    }
    catch {
      // url 不是完整 URL，需要与 host 拼接
    }

    // 只有 external 环境才使用代理路径
    const shouldUseProxy = process.env.VERSION_TYPE === "External";
    const effectiveDefaultPath = shouldUseProxy ? defaultPath : undefined;

    // 如果提供了默认路径，则添加默认路径
    if (effectiveDefaultPath) {
      const normalizedUrl = this.normalizePath(url);
      return new URL(`${effectiveDefaultPath}${normalizedUrl}`, host).toString();
    }

    // 直接拼接
    return new URL(url, host).toString();
  }

  /**
   * 标准化路径，确保以 / 开头
   */
  private normalizePath(path: string): string {
    return path.startsWith("/") ? path : `/${path}`;
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  get globalState() {
    return this.getBase(GlobalStateManager);
  }
}

export class Api extends HttpClient {
  constructor(ctx: ContextManager, config: HttpClientOptions) {
    super(ctx, config);
  }

  async getComposerModelList() {
    const username = this.globalState.get(GlobalState.USER_INFO)?.name;
    const { data } = await this.get<Model[]>(
      `/eapi/kwaipilot/plugin/ai_ide_chat_model?username=${username}`,
    );
    return data;
  }

  async getPlatformConfig() {
    const { data } = await this.get<PlatformConfigResponse>("/eapi/kwaipilot/plugin/config");
    return data;
  }

  async getCodeSearchPrompt(body: CodeSearchGeneratePromptRequest) {
    body.repoName = handleGitUrl(body.repoName);
    const { data } = await this.post<GetCodeSearchPromptResponse>("/eapi/kwaipilot/plugin/code/search/generate_prompt", JSON.stringify(body));

    return data;
  }

  async getCodeSearchCheckout(body: CodeSearchCheckoutRequest, signal: AbortSignal) {
    body.repoName = handleGitUrl(body.repoName);
    const { data } = await this.post<GetCodeSearchCheckoutResponse>("/eapi/kwaipilot/plugin/code/search/checkout", JSON.stringify(body), { signal });

    return data;
  }

  async getModelList() {
    const username = this.globalState.get(GlobalState.USER_INFO)?.name;
    const { data } = await this.get<Model[]>(`/eapi/kwaipilot/plugin/chat_model?username=${username}`);
    return data;
  }

  async getDocList() {
    const { data } = await this.get<Doc[]>("/eapi/kwaipilot/plugin/kwai_knowledge_repo_list");

    return data;
  }

  async getPrediction(cursorOffset: number, history: string[], language: string, id: string, signal: AbortSignal) {
    const body = {
      cursorOffset,
      history,
      language,
      id,
      platform: PLUGIN_PLATFORM,
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
    };
    const { data } = await this.post<GetPredictionResponse | null>("/eapi/kwaipilot/chat/edit/predict", JSON.stringify(body), { signal });
    return data;
    // const mockData: GetPredictionResponse = getMockData();
    // return mockData
  }

  /** 根据用户问题生成对话标题 */
  async summaryConversation(question: string) {
    const body = {
      platform: PLUGIN_PLATFORM,
      userQuestion: question,
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
    };
    return await this.rawRequest("/eapi/kwaipilot/plugin/tool/summaryConversation", {
      body: JSON.stringify(body),
      method: "POST",

    });
  }

  /** 获取推荐问题 */
  async getSuggestQuestion(params: { question: string; answer: string }) {
    const body = {
      ...params,
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
      platform: PLUGIN_PLATFORM,
    };
    const { data } = await this.post<string[]>("/eapi/kwaipilot/plugin/tool/get_suggest_question", JSON.stringify(body));
    return data;
  }

  async sendExistingAnnotationRequest(repoName: string, filePath: string) {
    const queryParams = {
      repoName,
      filePath,
      pageSize: "100",
      pageNum: "1",
    };

    const queryString = new URLSearchParams(queryParams).toString();

    const { data } = await this.get<{ list: ExistsAnnotation[] }>(`/eapi/kwaipilot/label/query/v2?${queryString}`);
    return data;
  }

  async reportCost(data: {
    beginTimestamp: number;
    namespace: string;
    stage: string;
    extra1?: string;
    extra2?: string;
  }) {
    const body = {
      beginTimestamp: data.beginTimestamp,
      duration: Date.now() - data.beginTimestamp,
      endTimestamp: Date.now(),
      namespace: data.namespace,
      platform: PLUGIN_PLATFORM,
      stage: data.stage,
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
      extra1: data.extra1,
      extra2: data.extra2,
    };
    return await this.post("/eapi/kwaipilot/log/time", JSON.stringify(body));
  }

  async reportEditFile(
    data: ReportEditFile,
  ) {
    const projectInfo = getProjectInfo();
    const body = {
      platform: PLUGIN_PLATFORM,
      projectName: projectInfo ? projectInfo[0].name : "unknodw project",
      username: this.globalState.get(GlobalState.USER_INFO)?.name,
      ...data,
    };
    // fixme: 本来是通过接口上报的，现在改成weblogger
    this.getBase(WebloggerManager).$reportUserAction({
      key: "code_storage",
      content: JSON.stringify(body),
    });
  }

  async fetchEventSource(url: string, options: FetchEventSourceInit): Promise<void> {
    return this.rawFetchEventSource(url, options);
  }

  async checkAgentGray(username: string) {
    return this.get<AgentGrayResponse>("/eapi/kwaipilot/plugin/agent_gray", { username });
  }

  async codeCompletionLog(param: CompletionLogParams) {
    return this.post("/eapi/kwaipilot/log/code/completions", JSON.stringify(param));
  }

  async fetchModelConfig() {
    const { data } = await this.get<ModelConfig[]>("/eapi/kwaipilot/plugin/agent/models", {}, {
      headers: {
        "kwaipilot-username": this.globalState.get(GlobalState.USER_INFO)?.name || "",
        "kwaipilot-platform": PLUGIN_PLATFORM,
      },
    });
    return data;
  }

  async saveMemory(params: {
    username: string;
    type: string;
    content: string;
    project: string;
    summary?: string;
    tags?: string[];
    metadata?: object;
    user_id?: string;
  }) {
    const body = {
      ...params,
    };

    // 使用 post 方法，它会自动处理 URL 构建和错误处理
    const response = await this.post<{
      id: number;
      username: string;
      type: string;
      content: string;
      summary?: string;
      tags?: string[];
      metadata?: object;
      project: string;
      status: number;
      created_at: string;
      updated_at: string;
    }>("/nodeapi/indexing/memory", JSON.stringify(body));

    return response;
  }

  async figmaOAuthToken(params: FigmaOAuthRequest) {
    const body = {
      ...params,
    };

    const response = await this.post<FigmaOAuthResponse>(
      "/nodeapi/indexing/auth-proxy/figma/oauth/access_token",
      JSON.stringify(body),
    );
    return response;
  }
}
