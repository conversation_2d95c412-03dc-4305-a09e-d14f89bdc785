import { CoreModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { LoggerManager } from "../../base/logger";
import { AgentModule } from "../agent";
import { ConfigManager, WorkspaceStateManager } from "../../base/state-manager";
import {
  Config,
  StateReturnType,
  WorkspaceState,
} from "../../base/state-manager/types";
import { getNonce } from "../../utils/getNonce";
import { Bridge } from "@bridge";
import { createExtensionRpcContext } from "../../base/bridge/ExtensionRpcContext";
import {
  NATIVE_BRIDGE_EVENT_NAME,
  WEBVIEW_BRIDGE_EVENT_NAME,
} from "shared/lib/bridge";
import { IRPCProtocol } from "shared/lib/bridge/proxyIdentifier";
import { ExtensionSettingsShape } from "shared/lib/bridge/protocol";
import { SettingPage } from "shared/lib/customSettingPanel";
import { upload } from "../../api/upload";
import { LOG_PATH } from "../../common/const";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { exec } from "child_process";
import { KwaipilotEnv } from "../../const";
import { t } from "i18next";
import { APP_NAME } from "shared/lib/const";
import dayjs from "dayjs";
import { genSqliteDataBySessionId } from "../../utils/sqlite";

export class SettingPanelModule
  extends CoreModule
  implements ExtensionSettingsShape {
  private readonly loggerScope = "SettingPanelModule";
  private panel: vscode.WebviewPanel | undefined;
  rpcContext: IRPCProtocol;
  private isInIDE = KwaipilotEnv.isInIde;
  /**
   * 获取设置面板的webview
   * @returns 设置面板的webview实例，如果不存在则返回undefined
   */
  public getPanelWebview() {
    return this.panel?.webview;
  }

  constructor(readonly ext: ContextManager) {
    super(ext);
    this.context.subscriptions.push(
      vscode.commands.registerCommand(
        `${APP_NAME}.openBasicsManagement`,
        (args) => {
          console.log("收到参数---->", args, typeof args);
          this.openCustomSettingPanel("basics");
        },
      ),
      vscode.commands.registerCommand(
        `${APP_NAME}.openFunctionManagement`,
        () => {
          this.openCustomSettingPanel("function");
        },
      ),
      vscode.commands.registerCommand(
        `${APP_NAME}.openCodeIndexManagement`,
        () => {
          this.openCustomSettingPanel("fileIndex");
        },
      ),
      vscode.commands.registerCommand(
        `${APP_NAME}.openWikiManagement`,
        () => {
          this.openCustomSettingPanel("fileIndex");
        },
      ),
      vscode.commands.registerCommand(`${APP_NAME}.openRulesManagement`, () => {
        this.openCustomSettingPanel("rules");
      }),
      vscode.commands.registerCommand(
        `${APP_NAME}.openMCPManagement`,
        (query) => {
          this.openCustomSettingPanel("mcp", query);
        },
      ),
      vscode.commands.registerCommand(
        `${APP_NAME}.openSettings`,
        () => this.openCustomSettingPanel("basics"),
      ),
    );
    this.rpcContext = createExtensionRpcContext({
      logger: () => this.getBase(LoggerManager),
      protocol: {
        onMessage: (listener) => {
          return this.getBase(Bridge).registerOneWayMessageHandler(
            NATIVE_BRIDGE_EVENT_NAME.RPC_MESSAGE,
            (data, source) => {
              if (source !== this.panel?.webview) {
                return;
              }
              listener(data);
            },
          );
        },
        send: (message) => {
          const webview = this.panel?.webview;
          if (!webview) {
            throw new Error("no webview");
          }
          this.getBase(Bridge).postOneWayMessage(
            webview,
            WEBVIEW_BRIDGE_EVENT_NAME.RPC_MESSAGE,
            message,
          );
        },
      },
    });
  }

  $getSettings(): StateReturnType["config"] {
    return {
      proxy: this.config.get(Config.PROXY_URL),
      [Config.MODEL_TYPE]: this.config.get(Config.MODEL_TYPE),
      [Config.ENABLE]: this.config.get(Config.ENABLE),
      [Config.COMMENT_COMPLETION_ENABLE]: this.config.get(
        Config.COMMENT_COMPLETION_ENABLE,
      ),
      [Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION]: this.config.get(
        Config.MAX_NEW_TOKENS_FOR_CODE_COMPLETION,
      ),
      [Config.CODE_COMPLETION_DELAY]: this.config.get(
        Config.CODE_COMPLETION_DELAY,
      ),
      [Config.ENABLE_CODE_BLOCK_ACTION]: this.config.get(
        Config.ENABLE_CODE_BLOCK_ACTION,
      ),
      [Config.PREDICTION_ENABLE]: this.config.get(Config.PREDICTION_ENABLE),
      [Config.ENABLE_LOCAL_AGENT]: this.config.get(Config.ENABLE_LOCAL_AGENT),
      [Config.AGENT_PREFERENCE]: this.config.get(Config.AGENT_PREFERENCE),
      [Config.DEFAULT_CHAT_MODE]: this.config.get(Config.DEFAULT_CHAT_MODE),
      [Config.ENABLE_DIAGNOSTICS_CHECK]: this.config.get(
        Config.ENABLE_DIAGNOSTICS_CHECK,
      ),
      [Config.COMPOSER_ENABLE_AUTO_RUN]: this.config.get(
        Config.COMPOSER_ENABLE_AUTO_RUN,
      ),
      [Config.COMPOSER_ENABLE_AUTO_RUN_MCP]: this.config.get(
        Config.COMPOSER_ENABLE_AUTO_RUN_MCP,
      ),
      [Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE]: this.config.get(
        Config.COMPOSER_AUTO_RUN_COMMAND_EXCLUDE,
      ),
      [Config.FIGMA_TOKEN]: this.config.get(Config.FIGMA_TOKEN),
      [Config.FIGMA_TOKEN_EXPIRE]: this.config.get(Config.FIGMA_TOKEN_EXPIRE),
      [Config.FIGMA_REFRESH_TOKEN]: this.config.get(Config.FIGMA_REFRESH_TOKEN),
      [Config.FIGMA_EMAIL]: this.config.get(Config.FIGMA_EMAIL),
      [Config.CUSTOM_DATABASE_PATH]: this.config.get(Config.CUSTOM_DATABASE_PATH),
      [Config.VERCEL_TOKEN]: this.config.get(Config.VERCEL_TOKEN),
      [Config.VERCEL_TOKEN_EXPIRE]: this.config.get(
        Config.VERCEL_TOKEN_EXPIRE,
      ),
      [Config.VERCEL_EMAIL]: this.config.get(Config.VERCEL_EMAIL),
      [Config.VERCEL_REFRESH_TOKEN]: this.config.get(
        Config.VERCEL_REFRESH_TOKEN,
      ),
      [Config.ENABLE_BROWSER_ACTION]: this.config.get(Config.ENABLE_BROWSER_ACTION),
    };
  }

  $updateSetting(
    key: keyof StateReturnType["config"],
    value: StateReturnType["config"][typeof key],
  ): void {
    this.config.update(key, value);
  }

  $changeKwaipilotLang(lang: string) {
    // 只在ide里有这个功能
    vscode.commands.executeCommand(`${APP_NAME}.action.setLanguage`, lang);
  }

  $openSettings(activePage?: SettingPage, query?: string) {
    this.openCustomSettingPanel(activePage, query);
  }

  async $generateHistoryLogUrl(startTimestamp: number, endTimestamp: number, sessionId: string): Promise<string | void> {
    // 声明变量在函数顶层
    let startDate: Date;
    let endDate: Date;
    let startDateStr: string;
    let endDateStr: string;
    let tarFilePath: string = "";
    let isBelongToday = false;
    let logResCdnUrl: string | undefined;

    try {
      // 转换为 Date 对象
      startDate = new Date(startTimestamp);
      endDate = new Date(endTimestamp);
      // 验证时间范围
      if (startDate > endDate) {
        vscode.window.showErrorMessage("消息时间范围无效");
        return;
      }
      const endDateDay = dayjs(endTimestamp);
      const today = dayjs();

      // 判断如果是今天的话
      if (endDateDay.format("YYYY-MM-DD") === today.format("YYYY-MM-DD")) {
        isBelongToday = true;
      }
      // 生成压缩包文件名，包含时间戳和时间范围
      const timestamp = new Date()
        .toISOString()
        .replace(/[-:]/g, "")
        .replace(/\..+/, "")
        .replace("T", "_");
      startDateStr = startDate.toISOString().split("T")[0].replace(/-/g, "");
      endDateStr = endDate.toISOString().split("T")[0].replace(/-/g, "");
      const tempDir = os.tmpdir();
      const tarFileName = `history_logs_${startDateStr}_to_${endDateStr}_${timestamp}.tar.gz`;
      tarFilePath = path.join(tempDir, tarFileName);
      // 检查日志目录是否存在
      if (!fs.existsSync(LOG_PATH)) {
        this.logger.warn("日志目录不存在", "generateHistoryLogUrl", {
          value: { path: LOG_PATH },
        });
        vscode.window.showWarningMessage(t("log.cannotGeneratePackage"));
        return;
      }
      // 显示进度提示
      await vscode.window.withProgress(
        {
          location: vscode.ProgressLocation.Notification,
          title: "正在生成历史日志包...",
          cancellable: false,
        },
        async (progress) => {
          progress.report({ increment: 0, message: "正在筛选历史日志文件..." });
          // 筛选出指定时间范围内的历史日志文件（.gz 压缩文件）
          const historyLogFiles: string[] = [];
          try {
            const files = fs.readdirSync(LOG_PATH);
            for (const file of files) {
              const filePath = path.join(LOG_PATH, file);
              const stat = fs.statSync(filePath);
              // 跳过目录，只处理文件
              if (!stat.isFile()) {
                continue;
              }
              // 从文件名中提取日期信息
              // 支持多种日期格式: 20250821-1800-01-mcp-error.log.gz, kwaipilot_2025-08-21.log 等
              // 匹配格式: YYYYMMDD-HHMM-xx-xxx.log.gz
              const dateMatch1 = file.match(/^(\d{4})(\d{2})(\d{2})-(\d{2})(\d{2})-/);
              if (dateMatch1) {
                const year = Number(dateMatch1[1]);
                const month = Number(dateMatch1[2]) - 1;
                const day = Number(dateMatch1[3]);
                const h = Number(dateMatch1[4]);
                const m = Number(dateMatch1[5]);
                const fileDate = new Date(year, month, day, h, m);
                const ts = fileDate.getTime();
                // 如果成功解析出日期，检查是否在指定范围内
                const f1 = dayjs(fileDate);
                const s1 = dayjs(startDate);
                // 历史日志
                if (ts && ts >= startTimestamp && (ts - endTimestamp <= 3600 * 1e3) && f1.format("YYYY-MM-DD") === s1.format("YYYY-MM-DD")) {
                // 限制单个文件大小不超过 100MB
                  if (stat.size < 100 * 1024 * 1024) {
                    historyLogFiles.push(file);
                  }
                  else {
                    this.logger.warn("跳过过大的历史日志文件", "generateHistoryLogUrl", {
                      value: { file, size: stat.size },
                    });
                  }
                }
              }
              // 匹配格式: xxx_YYYY-MM-DD.log 或 xxx_YYYY-MM-DD.log.1
              const dateMatch2 = file.match(/_(\d{4}-\d{2}-\d{2})\.log(\.\d+)?$/);
              if (dateMatch2) {
                const dateStr = dateMatch2[1];
                const fileDate = new Date(dateStr);
                // 如果成功解析出日期，检查是否在指定范围内
                const f1 = dayjs(fileDate);
                const s1 = dayjs(startDate);
                // 历史日志
                if (f1.format("YYYY-MM-DD") === s1.format("YYYY-MM-DD")) {
                // 限制单个文件大小不超过 100MB
                  if (stat.size < 100 * 1024 * 1024) {
                    historyLogFiles.push(file);
                  }
                  else {
                    this.logger.warn("跳过过大的历史日志文件", "generateHistoryLogUrl", {
                      value: { file, size: stat.size },
                    });
                  }
                }
              }

              // 今天的话，还需要把未压缩的所有 xxx.log 收集
              if (isBelongToday && /^[^\d]*\.log$/.test(file)) {
                historyLogFiles.push(file);
              }
            }
            this.logger.info("筛选出的历史日志文件", "generateHistoryLogUrl", {
              value: {
                files: historyLogFiles,
                totalFiles: files.length,
                timestampRange: `${startDateStr} 到 ${endDateStr}`,
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
              },
            });
          }
          catch (error) {
            this.logger.error("读取日志目录失败", "generateHistoryLogUrl", {
              err: error,
            });
            throw new Error("读取日志目录失败");
          }
          if (historyLogFiles.length === 0) {
            vscode.window.showWarningMessage(`在时间范围 ${startDateStr} 到 ${endDateStr} 内没有找到历史日志文件`);
            return;
          }

          // 加入 sqlite数据
          const sqlitePathInfo = await genSqliteDataBySessionId(sessionId);
          sqlitePathInfo && historyLogFiles.push(sqlitePathInfo.filename);

          progress.report({ increment: 30, message: "正在压缩历史日志文件..." });
          // 使用筛选出的文件列表进行压缩
          const fileList = historyLogFiles.map(file => `"${file}"`).join(" ");
          // 在日志目录内执行压缩，只压缩筛选出的历史日志文件
          const tarCommand = `cd "${LOG_PATH}" && tar -czf "${tarFilePath}" ${fileList}`;
          this.logger.info("开始压缩历史日志文件", "generateHistoryLogUrl", {
            value: {
              command: tarCommand,
              logPath: LOG_PATH,
              tarFile: tarFilePath,
              fileCount: historyLogFiles.length,
              files: historyLogFiles,
              timestampRange: `${startDateStr} 到 ${endDateStr}`,
            },
          });
          // 添加超时机制
          const compressionPromise = new Promise<void>((resolve, reject) => {
            const process = exec(
              tarCommand,
              { timeout: 120000 }, // 历史文件可能较大，增加到2分钟超时
              (error: any, stdout: string, stderr: string) => {
                if (error) {
                  if (error.code === "ETIMEDOUT") {
                    this.logger.error("历史日志压缩超时", "generateHistoryLogUrl", {
                      err: error,
                    });
                    reject(new Error("历史日志压缩超时"));
                  }
                  else {
                    this.logger.error("历史日志压缩失败", "generateHistoryLogUrl", {
                      err: error,
                      value: { stderr },
                    });
                    reject(error);
                  }
                  return;
                }
                this.logger.info("历史日志压缩完成", "generateHistoryLogUrl", {
                  value: { stdout, stderr },
                });
                resolve();
              },
            );
            // 手动超时处理
            const timeoutId = setTimeout(() => {
              process.kill();
              reject(new Error("历史日志压缩手动超时"));
            }, 120000);
            process.on("exit", () => {
              clearTimeout(timeoutId);
            });
          });
          await compressionPromise;
          progress.report({ increment: 40, message: "正在验证压缩包..." });
          // 检查压缩包是否创建成功
          if (!fs.existsSync(tarFilePath)) {
            throw new Error("历史日志压缩包创建失败");
          }
          // 检查压缩包大小
          const stats = fs.statSync(tarFilePath);
          this.logger.info("历史日志压缩包创建成功", "generateHistoryLogUrl", {
            value: { tarFile: tarFilePath, size: stats.size },
          });
          progress.report({ increment: 20, message: "压缩完成，准备上传..." });
          // 获取代理 URL
          const proxyUrl = this.config.get(Config.PROXY_URL);
          // 上传压缩包
          // 检查压缩包大小，如果太大则提示用户
          const maxUploadSize = 200 * 1024 * 1024; // 历史日志允许更大，200MB
          if (stats.size > maxUploadSize) {
            throw new Error(
              `历史日志压缩包过大 (${Math.round(stats.size / 1024 / 1024)}MB)，请缩小时间范围或联系管理员`,
            );
          }
          this.logger.info("开始上传历史日志压缩包", "generateHistoryLogUrl", {
            value: {
              tarFile: tarFilePath,
              proxyUrl,
              fileSize: stats.size,
              fileSizeMB: Math.round(stats.size / 1024 / 1024),
              timestampRange: `${startDateStr} 到 ${endDateStr}`,
            },
          });
          const uploadResult = await new Promise<any>((resolve, reject) => {
            // 添加上传超时，历史文件可能较大
            const uploadTimeout = setTimeout(
              () => {
                reject(
                  new Error("历史日志上传超时"),
                );
              },
              10 * 60 * 1000,
            ); // 10分钟上传超时
            upload(proxyUrl, {
              file: vscode.Uri.file(tarFilePath),
              onStart: (file) => {
                this.logger.info("开始上传历史日志", "generateHistoryLogUrl", {
                  value: {
                    file: file.filename,
                    size: stats.size,
                  },
                });
              },
              onProgress: (file) => {
                this.logger.info("历史日志上传进度", "generateHistoryLogUrl", {
                  value: {
                    progress: file.progress,
                    filename: file.filename,
                  },
                });
              },
              onSuccess: (data) => {
                clearTimeout(uploadTimeout);
                this.logger.info("历史日志上传成功", "generateHistoryLogUrl", { value: data });
                resolve(data);
              },
              onFailed: (file) => {
                clearTimeout(uploadTimeout);
                this.logger.error("历史日志上传失败", "generateHistoryLogUrl", {
                  value: file,
                });
                reject(
                  new Error(
                    `历史日志上传失败: ${file.filename}${file.error ? ` - ${file.error}` : ""}`,
                  ),
                );
              },
            });
          });
          // 复制 URL 到剪贴板并提示用户
          if (uploadResult && uploadResult.url) {
            this.logger.info("历史日志 URL 生成成功", "generateHistoryLogUrl", {
              value: { url: uploadResult.url, timestampRange: `${startDateStr} 到 ${endDateStr}` },
            });
            logResCdnUrl = uploadResult.url;
          }
          else {
            throw new Error("历史日志上传成功但未返回 URL");
          }
        },
      );
    }
    catch (error: any) {
      this.logger.error("生成历史日志 URL 失败", "generateHistoryLogUrl", {
        err: error,
        value: {
          message: error.message,
          stack: error.stack,
          code: error.code,
        },
      });
      // 根据错误类型提供更具体的错误信息
      let errorMessage = `生成历史日志 URL 失败: ${error.message}`;
      if (error.message.includes("压缩")) {
        errorMessage += "\n提示：可能是历史日志文件过大或磁盘空间不足";
      }
      else if (error.message.includes("上传")) {
        errorMessage += "\n提示：请检查网络连接和代理设置";
      }
      else if (error.message.includes("超时")) {
        errorMessage += "\n提示：操作超时，请缩小时间范围或稍后重试";
      }
      vscode.window.showErrorMessage(errorMessage);
    }
    finally {
      // 无论成功还是失败，都删除本地压缩包
      if (tarFilePath && fs.existsSync(tarFilePath)) {
        try {
          fs.unlinkSync(tarFilePath);
          this.logger.info("已删除本地历史日志压缩包", "generateHistoryLogUrl", {
            value: { tarFile: tarFilePath },
          });
        }
        catch (deleteError) {
          this.logger.warn("删除本地历史日志压缩包失败", "generateHistoryLogUrl", {
            value: {
              err: deleteError,
              tarFile: tarFilePath,
            },
          });
        }
      }
    }

    return logResCdnUrl;
  }

  async $generateLogUrl(isAutoCopyed = true): Promise<string | void> {
    let logResCdnUrl: string | undefined;
    // 生成压缩包文件名，包含时间戳
    const timestamp = new Date()
      .toISOString()
      .replace(/[-:]/g, "")
      .replace(/\..+/, "")
      .replace("T", "_");
    const tempDir = os.tmpdir();
    const tarFileName = `logs_backup_${timestamp}.tar.gz`;
    const tarFilePath = path.join(tempDir, tarFileName);
    try {
      // 检查日志目录是否存在
      if (!fs.existsSync(LOG_PATH)) {
        this.logger.warn("日志目录不存在", "generateLogUrl", {
          value: { path: LOG_PATH },
        });
        vscode.window.showWarningMessage(t("log.cannotGeneratePackage"));
        return;
      }
      // 显示进度提示
      await vscode.window.withProgress(
        {
          location: vscode.ProgressLocation.Notification,
          title: t("log.generatingPackage"),
          cancellable: false,
        },
        async (progress) => {
          progress.report({ increment: 0, message: t("log.filteringFiles") });
          // 先筛选出需要打包的文件，避免 tar 处理大量排除规则
          const logFiles: string[] = [];
          try {
            const files = fs.readdirSync(LOG_PATH);
            for (const file of files) {
              const filePath = path.join(LOG_PATH, file);
              const stat = fs.statSync(filePath);
              // 跳过目录，只处理文件
              if (!stat.isFile()) {
                continue;
              }
              // 只包含最近的日志文件（没有日期后缀的当前日志文件，以及小于 50MB 的日志文件）
              if (
                !file.endsWith(".gz")
                && !file.match(/.*-\d{8}-.*/)
                && !file.match(/.*_\d{8}_.*/)
                && !file.match(/.*\.\d{4}-\d{2}-\d{2}.*/)
                && !file.match(/^\d{8}-.*/)
                && file.match(/\.(log|txt)$|^(mcp|vscode|${APP_NAME}).*$/)
                && stat.size < 50 * 1024 * 1024
              ) {
                // 限制单个文件大小不超过 50MB
                logFiles.push(file);
              }
            }
            this.logger.info(t("log.message.filteredFiles"), "generateLogUrl", {
              value: { files: logFiles, totalFiles: files.length },
            });
          }
          catch (error) {
            this.logger.error(t("log.message.readDirectoryFailed"), "generateLogUrl", {
              err: error,
            });
            throw new Error(t("log.readLogDirectoryFailed"));
          }
          if (logFiles.length === 0) {
            vscode.window.showWarningMessage(t("log.noFilesToPackage"));
            return;
          }
          progress.report({ increment: 30, message: t("log.compressingFiles") });
          // 使用筛选出的文件列表进行压缩，避免压缩不需要的文件
          const fileList = logFiles.map(file => `"${file}"`).join(" ");
          // 在日志目录内执行压缩，只压缩筛选出的文件
          const tarCommand = `cd "${LOG_PATH}" && tar -czf "${tarFilePath}" ${fileList}`;
          this.logger.info(t("log.message.startCompression"), "generateLogUrl", {
            value: {
              command: tarCommand,
              logPath: LOG_PATH,
              tarFile: tarFilePath,
              fileCount: logFiles.length,
              files: logFiles,
            },
          });
          // 添加超时机制
          const compressionPromise = new Promise<void>((resolve, reject) => {
            const process = exec(
              tarCommand,
              { timeout: 60000 },
              (error: any, stdout: string, stderr: string) => {
                if (error) {
                  if (error.code === "ETIMEDOUT") {
                    this.logger.error(t("log.message.compressionTimeout"), "generateLogUrl", {
                      err: error,
                    });
                    reject(new Error(t("log.compressionTimeout")));
                  }
                  else {
                    this.logger.error(t("log.message.compressionFailed"), "generateLogUrl", {
                      err: error,
                      value: { stderr },
                    });
                    reject(error);
                  }
                  return;
                }
                this.logger.info(t("log.message.compressionComplete"), "generateLogUrl", {
                  value: { stdout, stderr },
                });
                resolve();
              },
            );
            // 手动超时处理
            const timeoutId = setTimeout(() => {
              process.kill();
              reject(new Error(t("log.compressionTimeoutManual")));
            }, 60000);
            process.on("exit", () => {
              clearTimeout(timeoutId);
            });
          });
          await compressionPromise;
          progress.report({ increment: 40, message: t("log.verifyingPackage") });
          // 检查压缩包是否创建成功
          if (!fs.existsSync(tarFilePath)) {
            throw new Error(t("log.compressionFailed"));
          }
          // 检查压缩包大小
          const stats = fs.statSync(tarFilePath);
          this.logger.info("压缩包创建成功", "generateLogUrl", {
            value: { tarFile: tarFilePath, size: stats.size },
          });
          progress.report({ increment: 20, message: t("log.compressionComplete") });
          // 获取代理 URL
          const proxyUrl = this.config.get(Config.PROXY_URL);
          // 上传压缩包
          // 检查压缩包大小，如果太大则提示用户
          const maxUploadSize = 100 * 1024 * 1024; // 100MB
          if (stats.size > maxUploadSize) {
            throw new Error(
              t("log.packageTooLarge", { sizeMB: Math.round(stats.size / 1024 / 1024) }),
            );
          }
          this.logger.info("开始上传日志压缩包", "generateLogUrl", {
            value: {
              tarFile: tarFilePath,
              proxyUrl,
              fileSize: stats.size,
              fileSizeMB: Math.round(stats.size / 1024 / 1024),
            },
          });
          const uploadResult = await new Promise<any>((resolve, reject) => {
            // 添加上传超时
            const uploadTimeout = setTimeout(
              () => {
                reject(
                  new Error(t("log.uploadTimeout")),
                );
              },
              5 * 60 * 1000,
            ); // 5分钟上传超时
            upload(proxyUrl, {
              file: vscode.Uri.file(tarFilePath),
              onStart: (file) => {
                this.logger.info(t("log.message.uploadStart"), "generateLogUrl", {
                  value: {
                    file: file.filename,
                    size: stats.size,
                  },
                });
              },
              onProgress: (file) => {
                this.logger.info(t("log.message.uploadProgress"), "generateLogUrl", {
                  value: {
                    progress: file.progress,
                    filename: file.filename,
                  },
                });
              },
              onSuccess: (data) => {
                clearTimeout(uploadTimeout);
                this.logger.info("上传成功", "generateLogUrl", { value: data });
                resolve(data);
              },
              onFailed: (file) => {
                clearTimeout(uploadTimeout);
                this.logger.error(t("log.message.uploadFailed"), "generateLogUrl", {
                  value: file,
                });
                reject(
                  new Error(
                    t("log.uploadFailedWithFile", {
                      filename: file.filename,
                      error: file.error ? ` - ${file.error}` : "",
                    }),
                  ),
                );
              },
            });
          });
          // 复制 URL 到剪贴板并提示用户
          if (uploadResult && uploadResult.url) {
            if (isAutoCopyed) {
              await vscode.env.clipboard.writeText(uploadResult.url);
              this.logger.info("日志 URL 生成成功", "generateLogUrl", {
                value: { url: uploadResult.url },
              });
              vscode.window.showInformationMessage(
                t("log.uploadSuccess", { url: uploadResult.url }),
              );
            }
            logResCdnUrl = uploadResult.url;
          }
          else {
            throw new Error(t("log.uploadReturnNoUrl"));
          }
        },
      );
    }
    catch (error: any) {
      this.logger.error(t("log.message.urlGenerationFailed"), "generateLogUrl", {
        err: error,
        value: {
          message: error.message,
          stack: error.stack,
          code: error.code,
        },
      });
      // 根据错误类型提供更具体的错误信息
      let errorMessage = t("log.uploadFailed", { message: error.message });
      if (error.message.includes("压缩")) {
        errorMessage += t("log.compressionHint");
      }
      else if (error.message.includes("上传")) {
        errorMessage += t("log.uploadHint");
      }
      else if (error.message.includes("超时")) {
        errorMessage += t("log.timeoutHint");
      }
      vscode.window.showErrorMessage(errorMessage);
    }
    finally {
      // 无论成功还是失败，都删除本地压缩包
      if (fs.existsSync(tarFilePath)) {
        try {
          fs.unlinkSync(tarFilePath);
          this.logger.info(t("log.message.packageDeleted"), "generateLogUrl", {
            value: { tarFile: tarFilePath },
          });
        }
        catch (deleteError) {
          this.logger.warn(t("log.message.packageDeleteFailed"), "generateLogUrl", {
            value: {
              err: deleteError,
              tarFile: tarFilePath,
            },
          });
        }
      }
    }

    return logResCdnUrl;
  }

  async $copyToClipboard(text: string, tip?: string) {
    await vscode.env.clipboard.writeText(text);
    tip && vscode.window.showInformationMessage(
      tip,
    );
  }

  async $selectSqliteDatabase(): Promise<string | void> {
    const fileUri = await vscode.window.showOpenDialog({
      canSelectFiles: true,
      canSelectFolders: false,
      canSelectMany: false,
      filters: {
        "SQLite Database": ["sqlite", "db", "sqlite3"],
      },
      openLabel: "选择数据库文件",
    });

    if (fileUri && fileUri[0]) {
      const selectedPath = fileUri[0].fsPath;

      // 验证是否为有效的 SQLite 数据库文件
      try {
        const fs = await import("fs");
        if (!fs.existsSync(selectedPath)) {
          vscode.window.showErrorMessage("选择的文件不存在");
          return;
        }

        // 更新配置
        this.config.update(Config.CUSTOM_DATABASE_PATH, selectedPath);

        // 显示成功消息
        vscode.window.showInformationMessage(
          `数据库路径已设置为: ${selectedPath}`,
          "重启应用生效",
        ).then((selection) => {
          if (selection === "重启应用生效") {
            vscode.commands.executeCommand("workbench.action.reloadWindow");
          }
        });

        return selectedPath;
      }
      catch (error) {
        vscode.window.showErrorMessage(`设置数据库路径失败: ${error}`);
        return;
      }
    }
  }

  async $showReloadWindowPrompt(message: string): Promise<void> {
    vscode.window.showInformationMessage(
      message,
      "重启应用生效",
    ).then((selection) => {
      if (selection === "重启应用生效") {
        vscode.commands.executeCommand("workbench.action.reloadWindow");
      }
    });
  }

  openCustomSettingPanel(
    activePage: SettingPage = "fileIndex",
    query?: string,
  ) {
    this.workspaceState.update(WorkspaceState.ACTIVE_SETTING_PAGE, activePage);
    if (this.panel) {
      this.panel.webview.html = this._getHtmlForWebview(
        this.panel.webview,
        this.context.extensionUri,
        query,
      );
      this.panel.reveal();
      return;
    }
    this.panel = vscode.window.createWebviewPanel(
      `${APP_NAME}-setting-panel`,
      t("kwaipilotSetting"),
      {
        viewColumn: vscode.ViewColumn.Active,
        preserveFocus: true,
      },
      {
        enableScripts: true,
        retainContextWhenHidden: true,
      },
    );
    // 设置图标
    const iconPath = {
      light: vscode.Uri.joinPath(
        this.context.extensionUri,
        "resources",
        "light",
        "settings-gear.svg",
      ),
      dark: vscode.Uri.joinPath(
        this.context.extensionUri,
        "resources",
        "dark",
        "settings-gear.svg",
      ),
    };
    this.panel.iconPath = iconPath;
    // 监听面板关闭事件
    this.panel.onDidDispose(
      () => {
        this.panel = undefined;
      },
      null,
      this.context.subscriptions,
    );
    const webview = this.panel.webview;
    // 监听 WebView 消息
    webview.onDidReceiveMessage((message) => {
      if (message.protocol === "callHandler") {
        this.getBase(Bridge).callNativeHandler(
          webview,
          message.name,
          message.data,
          message.callbackId,
        );
      }
      else if (message.protocol === "callback") {
        this.getBase(Bridge).handleCallback(message.callbackId, message.data);
      }
      else if (message.protocol === "message") {
        this.getBase(Bridge).handleOneWayMessage(webview, message.data);
      }
    });
    // 设置 WebView 内容
    this.panel.webview.html = this._getHtmlForWebview(
      this.panel.webview,
      this.context.extensionUri,
      query,
    );
  }

  $login(host?: "ide" | "plugin") {
    let cmd = `${APP_NAME}.login`;
    if (host === "ide") {
      cmd = `${APP_NAME}.loginIDE`;
    }
    return vscode.commands.executeCommand(cmd);
  }

  $logout(host?: "ide" | "plugin") {
    let cmd = `${APP_NAME}.logout`;
    if (host === "ide") {
      cmd = `${APP_NAME}.logoutIDE`;
    }
    return vscode.commands.executeCommand(cmd);
  }

  /**
   *【调用须知】 这个方法只有在 ${APP_NAME_PASCAL_CASE} IDE 里会提供，其他 IDE 不提供
   * @param productName
   */
  $importSettings(productName: string) {
    // 必须返回，因为需要 await 效果
    return vscode.commands.executeCommand(
      `${APP_NAME}.importSettings`,
      productName,
      false,
    );
  }

  $openIdeShortcutSettings() {
    vscode.commands.executeCommand("workbench.action.openGlobalKeybindings");
  }

  $openIdeUserSettings() {
    vscode.commands.executeCommand("workbench.action.openSettings");
  }

  /**
   * 获取webView视图
   * @param webview webView组件
   * @returns
   */
  private _getHtmlForWebview(
    webview: vscode.Webview,
    extensionUri: vscode.Uri,
    query?: string,
  ) {
    const inDevelopmentMode
      = this.context.extensionMode === vscode.ExtensionMode.Development;
    const vscMediaUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "setting-ui/assets"))
      .toString();
    const jsUrl = webview
      .asWebviewUri(vscode.Uri.joinPath(extensionUri, "bridge/index.js"))
      .toString();
    let scriptUri: string;
    let styleMainUri: string;
    if (!inDevelopmentMode) {
      scriptUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(extensionUri, "setting-ui/build/assets/index.js"),
        )
        .toString();
      styleMainUri = webview
        .asWebviewUri(
          vscode.Uri.joinPath(
            extensionUri,
            "setting-ui/build/assets/index.css",
          ),
        )
        .toString();
    }
    else {
      scriptUri = "http://localhost:5174/src/index.tsx";
      styleMainUri = "http://localhost:5174/src/App.css";
    }
    this.logger.info("init webview", "webview", {
      value: {
        scriptUri,
        styleMainUri,
        vscMediaUrl,
      },
    });
    const nonce = getNonce();
    const currentTheme = vscode.window.activeColorTheme;
    const isLight = currentTheme?.kind === 1 || currentTheme?.kind === 4;
    const proxyUrl = this.getBase(ConfigManager).get(Config.PROXY_URL);
    const editorLanguage = this.isInIDE ? vscode.env.language : "zh-cn";
    return `<!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link href="${styleMainUri}" rel="stylesheet">
          <script>window.vscMediaUrl = "${vscMediaUrl}"</script>
          <script>window.proxyUrl = "${proxyUrl}"</script>
          <script>window.ide = "vscode"</script>
          <script>window.colorThemeName = "dark"</script>
          <script>window.uriQuery = "${query}"</script>
          <script nonce="${nonce}" src="${jsUrl}"></script>
          <script>window.__KWAIPILOT_ENV__IN_IDE__ = ${this.isInIDE}</script>
          <script>window.editorLanguage = "${editorLanguage}"</script>
          <title>${APP_NAME}</title>
        </head>
        <body ${!isLight ? "class='dark'" : ""}>
          <div id="root"></div>
          ${
            inDevelopmentMode
              ? `<script type="module">
            import RefreshRuntime from "http://localhost:5174/@react-refresh"
            RefreshRuntime.injectIntoGlobalHook(window)
            window.$RefreshReg$ = () => {}
            window.$RefreshSig$ = () => (type) => type
            window.__vite_plugin_react_preamble_installed__ = true
            </script>
            <script type="module">
              import { createHotContext } from "http://localhost:5174/@vite/client"
              window.__vite_hot_context__ = createHotContext()
            </script>`
              : ""
          }
          <script type="module" nonce="${nonce}" src="${scriptUri}"></script>
        </body>
      </html>`;
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get agent() {
    return this.getCore(AgentModule);
  }

  private get workspaceState() {
    return this.getBase(WorkspaceStateManager);
  }

  private get config() {
    return this.getBase(ConfigManager);
  }
}
