// 用于控制直接启动 ${APP_NAME}-binary 启动模式 spawn | tcp
process.env.KWAIPILOT_SUBPROCESS_MODE = "spawn";

// 本地调试打开这个注释
// process.env.KWAIPILOT_SUBPROCESS_MODE = "tcp";

import { ExtensionContext } from "vscode";
import * as vscode from "vscode";
import { v4 as uuidv4 } from "uuid";
import { ContextManager } from "./base/context-manager";
import { Ast } from "./core/ast";
import { AnnotationTagService } from "./services/annotation-tag";
import { WebloggerManager } from "./base/weblogger";
import { LoginService } from "./services/login";
import { FigmaService } from "./services/figma";
import { DeploymentService } from "./services/deploy/DeploymentService";
import { VercelOauthService } from "./services/vercelOauth";
import { BrowserService } from "./services/browser";
import { QuickAskService } from "./services/quick-ask";
import { LoggerManager } from "./base/logger";
import { GlobalStateManager, WorkspaceStateManager, ConfigManager } from "./base/state-manager";
import { GlobalState } from "./base/state-manager/types";
import { Project } from "./core/project";
import { File } from "./core/file";
import { Api } from "./base/http-client";
import { DefaultBaseUrl, KwaipilotEnv } from "./const";
import { APP_NAME, APP_NAME_PASCAL_CASE, IS_EXTERNAL_OR_DEVELOPMENT } from "shared/lib/const";
import { DocumentManager } from "./core/document";
import { CompletionModule } from "./core/completion";
import { InlineChatService } from "./services/inline-chat";
import { DiffModule } from "./core/diff";
import { SqlLite } from "./services/sql-lite";
import { PredictionService } from "./services/prediction";
import { Bridge } from "@bridge";
import { Webview } from "@webview";
import { AgentModule } from "./core/agent";
import { SettingPanelModule } from "./core/setting-panel";
import { WriteToFileService } from "./services/write-to-file";
import { ComposerService } from "./services/composer";
import { ComposerSessionStorageService } from "./services/composer/ComposerSessionStorageService";
import { ApiConversationHistoryStorageService } from "./services/composer/ApiConversationHistoryStorageService";
import { LocalService } from "./core/localService";
import { ComposerHistoryStorageService } from "./services/composer/ComposerHistoryStorageService";
import { BridgeRegistry as VSCodeNativeBridgeRegistry } from "./services/bridge-registry";
import { IndexFileService } from "./services/index-file";
import { MCPService } from "./services/mcp";
import { RulesService } from "./services/rules";
import { DeveloperService } from "./services/developer";
import { ToLocalService } from "./services/to-loacl-message";
import { ThemeService } from "./services/theme";
import { MiscService } from "./services/misc";
import { BlockCodeService } from "./services/composer/BlockCodeService";
import { BlockCodeCacheStorageService } from "./services/composer/BlockCodeCacheStorageService";
import { createDeferred } from "./utils/deferred";
import { TerminalManager } from "./services/terminal";
import i18n from "i18next";
import en from "./i18n/en";
import zh from "./i18n/zh";
import { UIPreviewService } from "./services/ui-preview";
import { URIHandlerService } from "./services/uri-handler";
import { WikiService } from "./services/wiki";
import { PerformanceTrackerManager } from "./services/performance-tracker";
import { TokenUsageService } from "./services/token-usage";
import { PanelService } from "./services/panel/PanelService";
import { ErrorPanelManager } from "./services/error-panel/ErrorPanelManager";
import { ErrorPanelService } from "./services/error-panel/ErrorPanelService";
import { ChatImportExportService } from "./services/composer/ChatImportExportService";

/**
 * 为 Promise 添加超时处理
 * @param promise 待处理的 Promise
 * @param timeoutMs 超时时间（毫秒）
 * @param timeoutMessage 超时错误信息
 * @returns 带超时处理的 Promise
 */
function withTimeout<T>(promise: Promise<T>, timeoutMs: number, timeoutMessage: string): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs);
    }),
  ]);
}

async function checkCommandRegistration(commandId: string): Promise<boolean> {
  try {
    // 尝试获取命令信息来检查是否已注册
    const commands = await vscode.commands.getCommands();
    return commands.includes(commandId);
  }
  catch (error) {
    return false;
  }
}

KwaipilotEnv.setIsInIde(true);
KwaipilotEnv.hasJwtTokenInitedDeferred = process.env.VERSION_TYPE === "External" ? createDeferred<boolean | undefined>() : undefined;

/**
 * 通过命令获取 JWT Token
 * @returns Promise<string | undefined>
 */
export const getTokenByCommand = (): Promise<string | undefined> => {
  return new Promise((resolve, _) => {
    vscode.commands.executeCommand(`${APP_NAME}.getJwtToken`)
      .then((token: any) => {
        resolve(token);
      });
  });
};

/**
 * 通过环境变量获取当前存储的 JWT Token
 * @returns string | undefined
 */
const getCurrentStoredToken = (): string | undefined => {
  return KwaipilotEnv.getCurrentJwtToken();
};

const activateAsync = async (context: ExtensionContext) => {
  await i18n.init({
    resources: {
      en: {
        translation: en,
      },
      zh: {
        translation: zh,
      },
    },
    interpolation: {
      escapeValue: false,
    },
    lng: vscode.env.language, // ide跟着系统走
  });

  // #region 注册基础设施
  const ctx = new ContextManager(context);
  const globalState = new GlobalStateManager(ctx);
  ctx.registryBase(GlobalStateManager, globalState);

  const logger = new LoggerManager(ctx);
  ctx.registryBase(LoggerManager, logger);

  const workspaceState = new WorkspaceStateManager(ctx);
  ctx.registryBase(WorkspaceStateManager, workspaceState);

  const config = new ConfigManager(ctx);
  ctx.registryBase(ConfigManager, config);

  // 替换为新的桥接类
  const bridge = new Bridge(ctx);
  ctx.registryBase(Bridge, bridge);

  const weblogger = new WebloggerManager(ctx);
  ctx.registryBase(WebloggerManager, weblogger);

  KwaipilotEnv.tokenProvider = async (): Promise<string | undefined> => {
    logger.info("[KwaipilotTokenProvider] 开始获取 JWT Token", "tokenProvider");
    const startTime = Date.now();

    if (process.env.VERSION_TYPE !== "External") {
      logger.info(`[KwaipilotTokenProvider] 非外部版本，返回 undefined`, "tokenProvider");
      return undefined;
    }

    // 策略1: 先尝试获取当前已存储的 token
    try {
      const storedToken = getCurrentStoredToken();
      if (storedToken) {
        const elapsedTime = Date.now() - startTime;
        logger.info(`[KwaipilotTokenProvider] 从存储中获取到 Token，耗时: ${elapsedTime}ms`, "tokenProvider");
        return storedToken;
      }
    }
    catch (error) {
      logger.info("[KwaipilotTokenProvider] 从存储获取 Token 失败:", "tokenProvider");
    }

    // 策略2: 通过命令获取 token（带超时）
    try {
      logger.info("[KwaipilotTokenProvider] 尝试通过命令获取 Token", "tokenProvider");
      const commandToken = await withTimeout(
        getTokenByCommand(),
        5000,
        "通过命令获取 JWT Token 超时（5秒）",
      );

      if (commandToken) {
        const elapsedTime = Date.now() - startTime;
        logger.info(`[KwaipilotTokenProvider] 通过命令获取到 Token，耗时: ${elapsedTime}ms`, "tokenProvider");
        return commandToken;
      }
    }
    catch (error) {
      logger.info("[KwaipilotTokenProvider] 通过命令获取 Token 失败:", "tokenProvider");
    }

    // 策略3: 等待 JWT Token 初始化完成
    if (KwaipilotEnv.hasJwtTokenInitedDeferred?.promise) {
      try {
        logger.info("[KwaipilotTokenProvider] 等待 JWT Token 初始化", "tokenProvider");
        await withTimeout(
          KwaipilotEnv.hasJwtTokenInitedDeferred.promise,
          5000,
          "等待 JWT Token 初始化超时（5秒）",
        );

        const initializedToken = KwaipilotEnv.getCurrentJwtToken();
        if (initializedToken) {
          const elapsedTime = Date.now() - startTime;
          logger.info(`[KwaipilotTokenProvider] 等待初始化获取到 Token，耗时: ${elapsedTime}ms`, "tokenProvider");
          return initializedToken;
        }
        else {
          logger.info("[KwaipilotTokenProvider] 等待初始化获取到 Token 为 undefined", "tokenProvider");
        }
      }
      catch (error) {
        logger.info("[KwaipilotTokenProvider] 等待初始化获取 Token 失败:", "tokenProvider");
      }
    }

    // 所有策略都失败
    const elapsedTime = Date.now() - startTime;
    logger.info(`[KwaipilotTokenProvider] 所有获取 Token 的策略都失败，总耗时: ${elapsedTime}ms`, "tokenProvider");
    return undefined;
  };

  ctx.registryBase(Api, new Api(ctx, {
    defaultBaseUrl: DefaultBaseUrl || "",
    tokenProvider: KwaipilotEnv.tokenProvider,
  }));

  const webview = new Webview(ctx);
  ctx.registryBase(Webview, webview);

  // #endregion

  // #region 注册核心服务
  ctx.registryCore(Ast, new Ast(ctx));
  ctx.registryCore(Project, new Project(ctx));
  ctx.registryCore(File, new File(ctx));
  ctx.registryCore(DocumentManager, new DocumentManager(ctx));
  ctx.registryCore(CompletionModule, new CompletionModule(ctx));
  ctx.registryCore(DiffModule, new DiffModule(ctx));
  ctx.registryCore(SettingPanelModule, new SettingPanelModule(ctx));
  ctx.registryCore(LocalService, new LocalService(ctx));
  ctx.registryCore(AgentModule, new AgentModule(ctx));
  // #endregion

  // #region 注册业务域
  ctx.registryService(LoginService, new LoginService(ctx));
  ctx.registryService(FigmaService, new FigmaService(ctx));
  ctx.registryService(DeploymentService, new DeploymentService(ctx));

  // 只有对外版本才注册Vercel服务
  if (IS_EXTERNAL_OR_DEVELOPMENT) {
    ctx.registryService(VercelOauthService, new VercelOauthService(ctx));
  }
  ctx.registryService(BrowserService, new BrowserService(ctx));
  ctx.registryService(TerminalManager, new TerminalManager(ctx));

  const quickAsk = new QuickAskService(ctx);
  ctx.registryService(QuickAskService, quickAsk);

  // const codeAction = new CodeActionService(ctx);
  // ctx.registryService(CodeActionService, codeAction);

  // inline-chat
  ctx.registryService(InlineChatService, new InlineChatService(ctx));

  // activateCommentTag(context, chatProvider);
  ctx.registryService(AnnotationTagService, new AnnotationTagService(ctx));

  ctx.registryService(SqlLite, new SqlLite(ctx));
  ctx.registryService(TokenUsageService, new TokenUsageService(ctx));

  ctx.registryService(PredictionService, new PredictionService(ctx));
  ctx.registryService(WriteToFileService, new WriteToFileService(ctx));

  ctx.registryService(ComposerSessionStorageService, new ComposerSessionStorageService(ctx));
  ctx.registryService(ComposerHistoryStorageService, new ComposerHistoryStorageService(ctx));
  ctx.registryService(ApiConversationHistoryStorageService, new ApiConversationHistoryStorageService(ctx));

  ctx.registryService(ComposerService, new ComposerService(ctx));
  ctx.registryService(ChatImportExportService, new ChatImportExportService(ctx));

  ctx.registryService(ThemeService, new ThemeService(ctx));
  ctx.registryService(IndexFileService, new IndexFileService(ctx));
  ctx.registryService(WikiService, new WikiService(ctx));
  ctx.registryService(MCPService, new MCPService(ctx));
  ctx.registryService(RulesService, new RulesService(ctx));
  ctx.registryService(DeveloperService, new DeveloperService(ctx));
  ctx.registryService(ToLocalService, new ToLocalService(ctx));
  ctx.registryService(MiscService, new MiscService(ctx));
  ctx.registryService(BlockCodeService, new BlockCodeService(ctx));
  ctx.registryService(BlockCodeCacheStorageService, new BlockCodeCacheStorageService(ctx));
  ctx.registryService(PerformanceTrackerManager, new PerformanceTrackerManager(ctx));
  ctx.registryService(VSCodeNativeBridgeRegistry, new VSCodeNativeBridgeRegistry(ctx));
  ctx.registryService(UIPreviewService, new UIPreviewService(ctx));
  ctx.registryService(URIHandlerService, new URIHandlerService(ctx));

  // 错误面板

  const panelService = new PanelService(ctx);
  ctx.registryService(PanelService, panelService);
  // ErrorPanelManager 现在在 PanelService 构造函数中自动创建和注册
  const errorPanelManager = ctx.getService(ErrorPanelManager);
  const errorPanelService = new ErrorPanelService(ctx, errorPanelManager);
  ctx.registryService(ErrorPanelService, errorPanelService);

  // #endregion

  // 注册JWT token同步命令
  vscode.commands.registerCommand(`${APP_NAME}.bridge.syncJwtToken`, (token: string | undefined) => {
    logger.info(`JWT Token 同步命令被调用，token 存在: ${!!token}`, "tokenProvider");

    KwaipilotEnv.setCurrentJwtToken(token);
    KwaipilotEnv.hasJwtTokenInitedDeferred?.resolve(true);
    ctx.getCore(LocalService).sendMessage("state/jwtToken", { jwtToken: token });

    logger.info("JWT Token 同步完成", "tokenProvider");
  });

  return ctx;
};
export function activate(context: ExtensionContext) {
  if (context.extensionMode === vscode.ExtensionMode.Development) {
    // @ts-expect-error for testing
    globalThis._vscode = vscode;
    // @ts-expect-error for testing
    globalThis._context = context;
  }
  let deviceId: string | undefined = context.globalState.get(`${APP_NAME}.deviceId`);
  if (!deviceId) {
    deviceId = uuidv4();
    context.globalState.update(`${APP_NAME}.deviceId`, deviceId);
  }

  vscode.commands.registerCommand(`${APP_NAME}.bridge.userInfo`, async (userInfo: any) => {
    // @IMP: 一定要等这个事件触发完，再执行下面的事情
    await vscode.commands.executeCommand(`${APP_NAME}.syncUserInfoFromIDE`, userInfo);
    context.globalState.update(GlobalState.USER_INFO, userInfo);
  });

  // 先启动异步激活过程
  activateAsync(context).then((ctx) => {
    console.log(`[${APP_NAME_PASCAL_CASE}] Extension activation extension-export`);
    vscode.commands.executeCommand(`${APP_NAME}.bridge.extensionHostReady`);

    checkCommandRegistration(`${APP_NAME}.bridge.extensionHostReady`).then((isRegistered) => {
      if (!isRegistered) {
        ctx.getBase(LoggerManager).error("entry", `[${APP_NAME_PASCAL_CASE}] extensionHostReady 命令未找到，跳过此步骤`);
      }
    });
  });
}

export function deactivate() { }
