import { ExtensionContext } from "vscode";
import * as vscode from "vscode";
import { v4 as uuidv4 } from "uuid";
import { ContextManager } from "./base/context-manager";
import { Webview } from "@webview";
import { Ast } from "./core/ast";
// import { AnnotationTagService } from "./services/annotation-tag";
import { WebloggerManager } from "./base/weblogger";
import { LoginService } from "./services/login";
import { FigmaService } from "./services/figma";
import { DeploymentService } from "./services/deploy/DeploymentService";
import { VercelOauthService } from "./services/vercelOauth";
import { BrowserService } from "./services/browser";
import { QuickAskService } from "./services/quick-ask";
import { CodeActionService } from "./services/code-action";
import { LoggerManager } from "./base/logger";
import { GlobalStateManager, WorkspaceStateManager, ConfigManager } from "./base/state-manager";
import { Project } from "./core/project";
import { File } from "./core/file";
import { Api } from "./base/http-client";
import { DefaultBaseUrl } from "./const";
import { DocumentManager } from "./core/document";
import { CompletionModule } from "./core/completion";
import { InlineChatService } from "./services/inline-chat";
import { DiffModule } from "./core/diff";
import { SqlLite } from "./services/sql-lite";
import { PredictionService } from "./services/prediction";
import { Bridge } from "@bridge";
import { AgentModule } from "./core/agent";
import { SettingPanelModule } from "./core/setting-panel";
import { WriteToFileService } from "./services/write-to-file";
import { ComposerService } from "./services/composer";
import { ComposerSessionStorageService } from "./services/composer/ComposerSessionStorageService";
import { ApiConversationHistoryStorageService } from "./services/composer/ApiConversationHistoryStorageService";
import { LocalService } from "./core/localService";
import { ComposerHistoryStorageService } from "./services/composer/ComposerHistoryStorageService";
import { BridgeRegistry } from "./services/bridge-registry";
import { IndexFileService } from "./services/index-file";
import { MCPService } from "./services/mcp";
import { RulesService } from "./services/rules";
import { DeveloperService } from "./services/developer";
import { ToLocalService } from "./services/to-loacl-message";
import { ThemeService } from "./services/theme";
import { GlobalState } from "shared/lib/state-manager/types";
import { MiscService } from "./services/misc";
import { URIHandlerService } from "./services/uri-handler";
import { UIPreviewService } from "./services/ui-preview";
import { TerminalManager } from "./services/terminal";
import { BlockCodeService } from "./services/composer/BlockCodeService";
import { BlockCodeCacheStorageService } from "./services/composer/BlockCodeCacheStorageService";
import i18n from "i18next";
import en from "./i18n/en";
import zh from "./i18n/zh";
import { PerformanceTrackerManager } from "./services/performance-tracker";
import { WikiService } from "./services/wiki";
import { PanelService } from "./services/panel/PanelService";
import { ErrorPanelManager } from "./services/error-panel/ErrorPanelManager";
import { ErrorPanelService } from "./services/error-panel/ErrorPanelService";
import { TokenUsageService } from "./services/token-usage";
import { IS_EXTERNAL_OR_DEVELOPMENT } from "shared/lib/const";
import { ChatImportExportService } from "./services/composer/ChatImportExportService";

const activateAsync = async (context: ExtensionContext) => {
  await i18n.init({
    resources: {
      en: {
        translation: en,
      },
      zh: {
        translation: zh,
      },
    },
    interpolation: {
      escapeValue: false,
    },
    lng: "zh", // 插件始终用中文
  });

  // 测试mock用：设置环境变量，使CLOUDDEV_CONTAINER为"1"表示为clouddev环境
  // process.env.CLOUDDEV_CONTAINER = "1";
  // process.env.CLOUDDEV_USER = "liuzhengzheng";
  // #region 注册基础设施
  const ctx = new ContextManager(context);
  const globalState = new GlobalStateManager(ctx);
  ctx.registryBase(GlobalStateManager, globalState);

  const workspaceState = new WorkspaceStateManager(ctx);
  ctx.registryBase(WorkspaceStateManager, workspaceState);

  const config = new ConfigManager(ctx);
  ctx.registryBase(ConfigManager, config);

  const bridge = new Bridge(ctx);
  ctx.registryBase(Bridge, bridge);

  const weblogger = new WebloggerManager(ctx);
  ctx.registryBase(WebloggerManager, weblogger);

  const logger = new LoggerManager(ctx);
  ctx.registryBase(LoggerManager, logger);
  ctx.registryBase(Api, new Api(ctx, { defaultBaseUrl: DefaultBaseUrl }));

  const webview = new Webview(ctx);
  ctx.registryBase(Webview, webview);

  // #endregion

  // #region 注册核心服务
  ctx.registryCore(Ast, new Ast(ctx));
  ctx.registryCore(Project, new Project(ctx));
  ctx.registryCore(File, new File(ctx));
  ctx.registryCore(DocumentManager, new DocumentManager(ctx));
  ctx.registryCore(CompletionModule, new CompletionModule(ctx));
  ctx.registryCore(DiffModule, new DiffModule(ctx));
  ctx.registryCore(SettingPanelModule, new SettingPanelModule(ctx));
  ctx.registryCore(LocalService, new LocalService(ctx));
  ctx.registryCore(AgentModule, new AgentModule(ctx));
  // #endregion

  // #region 注册业务域
  ctx.registryService(LoginService, new LoginService(ctx));
  ctx.registryService(FigmaService, new FigmaService(ctx));
  ctx.registryService(DeploymentService, new DeploymentService(ctx));

  // 只有对外版本才注册Vercel服务
  if (IS_EXTERNAL_OR_DEVELOPMENT) {
    ctx.registryService(VercelOauthService, new VercelOauthService(ctx));
  }
  ctx.registryService(BrowserService, new BrowserService(ctx));
  ctx.registryService(TerminalManager, new TerminalManager(ctx));

  const quickAsk = new QuickAskService(ctx);
  ctx.registryService(QuickAskService, quickAsk);

  const codeAction = new CodeActionService(ctx);
  ctx.registryService(CodeActionService, codeAction);

  // inline-chat
  ctx.registryService(InlineChatService, new InlineChatService(ctx));

  // activateCommentTag(context, chatProvider);
  // ctx.registryService(AnnotationTagService, new AnnotationTagService(ctx));

  ctx.registryService(SqlLite, new SqlLite(ctx));
  ctx.registryService(TokenUsageService, new TokenUsageService(ctx));

  ctx.registryService(PredictionService, new PredictionService(ctx));
  ctx.registryService(WriteToFileService, new WriteToFileService(ctx));

  ctx.registryService(ComposerSessionStorageService, new ComposerSessionStorageService(ctx));
  ctx.registryService(ComposerHistoryStorageService, new ComposerHistoryStorageService(ctx));
  ctx.registryService(ApiConversationHistoryStorageService, new ApiConversationHistoryStorageService(ctx));

  ctx.registryService(ComposerService, new ComposerService(ctx));
  ctx.registryService(ChatImportExportService, new ChatImportExportService(ctx));
  ctx.registryService(IndexFileService, new IndexFileService(ctx));
  ctx.registryService(WikiService, new WikiService(ctx));
  ctx.registryService(MCPService, new MCPService(ctx));
  ctx.registryService(RulesService, new RulesService(ctx));
  ctx.registryService(DeveloperService, new DeveloperService(ctx));
  ctx.registryService(ToLocalService, new ToLocalService(ctx));
  ctx.registryService(MiscService, new MiscService(ctx));
  ctx.registryService(URIHandlerService, new URIHandlerService(ctx));
  ctx.registryService(UIPreviewService, new UIPreviewService(ctx));

  ctx.registryService(BridgeRegistry, new BridgeRegistry(ctx));

  ctx.registryService(ThemeService, new ThemeService(ctx));

  ctx.registryService(BlockCodeService, new BlockCodeService(ctx));
  ctx.registryService(BlockCodeCacheStorageService, new BlockCodeCacheStorageService(ctx));
  ctx.registryService(PerformanceTrackerManager, new PerformanceTrackerManager(ctx));

  // 错误面板
  const panelService = new PanelService(ctx);
  ctx.registryService(PanelService, panelService);

  // ErrorPanelManager 现在在 PanelService 构造函数中自动创建和注册
  const errorPanelManager = ctx.getService(ErrorPanelManager);

  const errorPanelService = new ErrorPanelService(ctx, errorPanelManager);
  ctx.registryService(ErrorPanelService, errorPanelService);

  let deviceId: string | undefined = globalState.get(GlobalState.DEVICE_ID);
  if (!deviceId) {
    deviceId = uuidv4();
    globalState.update(GlobalState.DEVICE_ID, deviceId);
  }

  // #endregion
};
export function activate(context: ExtensionContext) {
  if (context.extensionMode === vscode.ExtensionMode.Development) {
    // @ts-expect-error for testing
    globalThis._vscode = vscode;
    // @ts-expect-error for testing
    globalThis._context = context;
  }
  activateAsync(context);
}

export function deactivate() { }
