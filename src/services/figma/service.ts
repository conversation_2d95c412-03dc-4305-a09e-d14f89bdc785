import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { ConfigManager, GlobalStateManager } from "../../base/state-manager";
import {
  Config,
  FIGMA_CLIENT_ID,
  FIGMA_CLIENT_ID_OUTER,
  FIGMA_REDIRECT_URI_VSCODE,
  FIGMA_REDIRECT_URI_KWAIPILOT,
} from "../../base/state-manager/types";
import { WebloggerManager } from "../../base/weblogger";
import { Webview } from "@webview";
import * as vscode from "vscode";
import fetch from "node-fetch";
import { Bridge } from "@bridge";
import { NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { v4 as uuidv4 } from "uuid";
import { t } from "i18next";
import { KwaipilotEnv } from "../../const";
import { APP_NAME, APP_NAME_PASCAL_CASE, IS_EXTERNAL } from "shared/lib/const";
import { FigmaRestApi } from "./restApi";
import { Api } from "../../base/http-client";

export class FigmaService extends ServiceModule {
  private figmaRestApi: FigmaRestApi;

  constructor(ext: ContextManager) {
    super(ext);
    this.figmaRestApi = new FigmaRestApi(this.getBase(ConfigManager));
    this.registryListener();
    this.checkStatus();
  }

  private get httpClient() {
    return this.getBase(Api);
  }

  private registryListener() {
    this.getBase(Bridge).registerHandler(NATIVE_BRIDGE_EVENT_NAME.FIGMA_OAUTH, this.handleLogin.bind(this));
    this.getBase(Bridge).registerHandler(NATIVE_BRIDGE_EVENT_NAME.FIGMA_LINK, this.handleLink.bind(this));
    this.getBase(Bridge).registerHandler(NATIVE_BRIDGE_EVENT_NAME.FIGMA_REFRESH_TOKEN, this.handleRefreshToken.bind(this));
    this.getBase(Bridge).registerHandler(NATIVE_BRIDGE_EVENT_NAME.FIGMA_GET_NODE_IMAGE, this.handleGetNodeImage.bind(this));
    // vscode.commands.registerCommand(`${APP_NAME}.figma.oauth`, this.login.bind(this));
    // vscode.commands.registerCommand(`${APP_NAME}.figma.logout`, this.logout.bind(this));
  }

  private async handleLink(data?: any) {
    await vscode.env.openExternal(vscode.Uri.parse(data.url));
  }

  private handleLogin(data?: unknown) {
    // 只处理Figma类型的登录请求
    const payload = data as { type?: string } | undefined;
    if (payload?.type === "figma") {
      this.login();
    }
  }

  private handleRefreshToken(data?: unknown) {
    const payload = data as { type?: string } | undefined;
    if (payload?.type === "figmaRefresh") {
      return this.refreshToken();
    }
    return false;
  }

  private getFigmaOAuthUrl(state: string) {
    const baseUrl = "https://www.figma.com/oauth";
    const params = new URLSearchParams({
      client_id: IS_EXTERNAL ? FIGMA_CLIENT_ID_OUTER : FIGMA_CLIENT_ID,
      redirect_uri: KwaipilotEnv.isInIde ? FIGMA_REDIRECT_URI_KWAIPILOT : FIGMA_REDIRECT_URI_VSCODE,
      scope: "current_user:read,files:read,file_variables:read,file_dev_resources:read", // 基础文件读取权限，可根据需要调整
      state: state,
      response_type: "code",
    });

    return `${baseUrl}?${params.toString()}`;
  }

  async login() {
    // 生成状态参数用于安全验证
    const state = uuidv4();
    this.context.globalState.update("figmaOAuthState", state);

    const figmaOAuthUrl = this.getFigmaOAuthUrl(state);

    try {
      // 打开Figma OAuth页面
      await vscode.env.openExternal(vscode.Uri.parse(figmaOAuthUrl));

      vscode.window.showInformationMessage(t("figmaOAuthPageOpened"));

      // 这里可以添加后续的回调处理逻辑
      // 目前只实现打开OAuth页面的功能
    }
    catch (error) {
      vscode.window.showErrorMessage(t("figmaOAuthFailedToOpenPage"));
    }
  }

  async refreshToken() {
    try {
      // 获取存储的refresh token
      const figmaRefreshToken = this.config.get(Config.FIGMA_REFRESH_TOKEN);
      const resp = await this.httpClient.figmaOAuthToken({
        refresh_token: figmaRefreshToken,
        grant_type: "refresh_token",
        environment: IS_EXTERNAL ? "outer" : "inner",
      });
      const respData = resp.data;
      if (respData.access_token) {
        // 保存新的 access token
        this.config.update(Config.FIGMA_TOKEN, respData.access_token);

        // 计算并保存新的过期时间
        if (respData.expires_in) {
          const expireTimestamp = Date.now() + respData.expires_in * 1000;
          this.config.update(Config.FIGMA_TOKEN_EXPIRE, expireTimestamp);
        }

        vscode.window.showInformationMessage(t("figmaTokenRefreshed"));
        return true;
      }
      else {
        vscode.window.showErrorMessage(t("figmaOAuthFailedToRefreshToken", { msg: resp.message || "未知错误" }));
        return false;
      }
    }
    catch (error) {
      vscode.window.showErrorMessage(t("figmaOAuthFailedToRefreshTokenError", { msg: error instanceof Error ? error.message : String(error) }));
      return false;
    }
  }

  // private logout() {
  //   // 清除Figma相关的状态信息
  //   this.context.globalState.update("figmaOAuthState", undefined);
  //   this.config.update(Config.FIGMA_TOKEN, "");
  //   vscode.commands.executeCommand("setContext", "kwaipilot.figmaLogin", false);
  //   vscode.window.showInformationMessage("Figma账户已注销！");
  // }

  async checkStatus() {
    // 检查是否已有Figma访问令牌
    const accessToken = this.config.get(Config.FIGMA_TOKEN);

    if (accessToken) {
      vscode.commands.executeCommand("setContext", `${APP_NAME}.figmaLogin`, true);
    }
    else {
      vscode.commands.executeCommand("setContext", `${APP_NAME}.figmaLogin`, false);
    }
  }

  async showLoginMessage() {
    const selection = await vscode.window.showInformationMessage(
      `${APP_NAME_PASCAL_CASE}：请连接您的Figma账户以使用相关功能！`,
      "连接Figma",
      "取消",
    );

    if (selection === "连接Figma") {
      this.login();
    }
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  private get config() {
    return this.getBase(ConfigManager);
  }

  private async handleGetNodeImage(data?: { fileKey: string; nodeId: string }): Promise<{
    success: boolean;
    imageUrl?: string;
    base64?: string;
    error?: string;
  }> {
    try {
      const { fileKey, nodeId } = data || {};

      if (!fileKey || !nodeId) {
        return {
          success: false,
          error: "fileKey or nodeId is required",
        };
      }

      const nodeIdStr = nodeId.includes("-") ? nodeId.replace(/-/g, ":") : nodeId;
      const { imageUrl, msg } = await this.figmaRestApi.getNodeImageUrl(fileKey, nodeIdStr, {
        format: "png",
        scale: 2,
      });

      if (!imageUrl) {
        return {
          success: false,
          error: msg || `fetch figma node ${nodeId} image failed`,
        };
      }

      const response = await fetch(imageUrl);

      if (!response.ok) {
        return {
          success: false,
          error: `fetch figma image content failed: ${response.statusText}`,
        };
      }

      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const base64 = buffer.toString("base64");
      return {
        success: true,
        imageUrl,
        base64: `data:image/png;base64,${base64}`,
      };
    }
    catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "unknown error",
      };
    }
  }
}
