import { ServiceModule } from "..";
import { Bridge } from "@bridge";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import * as os from "os";
import * as path from "path";
import { DiffBlock, NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { DIFF_VIEW_SNAPSHOT_MODIFIED_PREFIX, DIFF_VIEW_URI_SCHEME, DiffViewProvider } from "../../core/diff/diffViewProvider";
import { fileExistsAtPath } from "../../utils/fs";
import { DiffModule } from "../../core/diff";
import { LoggerManager } from "../../base/logger";
import * as fs from "fs/promises";
import { v4 as uuidv4 } from "uuid";
import { GlobalStateManager, WorkspaceStateManager } from "../../base/state-manager";
import { GlobalState, WorkspaceState } from "shared/lib/state-manager/types";
import { PLUGIN_PLATFORM } from "../../log/Model";
import { Api } from "../../base/http-client";
import { EditFileRequest, EditFileResponse, MultiDiffContent, ReplaceInFileRequest, WriteFileRequest } from "shared/lib/agent";
import { createDeferred, Deferred } from "../../utils/deferred";
import { arePathsEqual } from "../../utils/path";
import { fixModelHtmlEscaping, removeInvalidChars } from "../../utils/string";
import { WebloggerManager } from "../../base/weblogger";
import { ReportOpt } from "shared/lib/misc/logger";
import { ComposerService } from "../composer";
import { ReportEditFile } from "../../base/http-client/interface";
import { APP_DIFF_VIEW_LABEL, APP_NAME } from "shared/lib/const";
import replaceInFile from "replace-in-file";
import { readFile } from "fs/promises";
import { t } from "i18next";
import { serializeError } from "serialize-error";

// 错误类型定义
type FileProcessError = {
  filePath: string;
  errorType: "timeout" | "aborted" | "processing" | "content" | "network" | "unknown";
  message: string;
  timestamp: number;
};

type WriteToFilePayload = {
  path: string;
  content?: string;
  diff?: string;
  isFinal?: boolean;
  processingMode?: "line-by-line" | "full-content";
};

/* editFile 所在消息的上下文信息 */
export interface EditFileContext {
  sessionId?: string;
  chatId?: string;
  /* 触发 editFile 的来源: 新版助理模式 or 旧版 markdown 编辑器（对话模式、instant-apply） */
  source: "agent-v2" | "markdown-body" | "inline-chat";
}

interface EditFileState {
  context: EditFileContext;
  messageQueue: MessageQueue | undefined;
  deferred: Deferred<EditFileResponse>;
  requestController: AbortController | null;
  applyId: string;
}
interface DiffState {
  diffViewProvider: DiffViewProvider;
  context: EditFileContext;
  applyId: string;
}
export class WriteToFileService extends ServiceModule {
  private loggerScope = "WriteToFileService";

  /**
   * 在完成 editFile 之后 diff 时保存的状态
   */
  diffState: Map<string, DiffState> = new Map();
  /**
   * 在执行 editFile 期间（从调用.editFile 到用户确认（accept/reject））存储的状态，
   *
   * key: 文件路径
   * value: 文件状态 {EditFileState}
   */
  state = new Map<string, EditFileState>();

  private fileProcessingQueue: string[] = [];
  private isProcessingFile = false;
  private processingLock = false;
  private recentErrors: Map<string, FileProcessError> = new Map(); // 记录最近的错误
  private cwd: string;
  private diffContentProvider: vscode.TextDocumentContentProvider | undefined;
  constructor(private readonly ctx: ContextManager) {
    super(ctx);
    this.cwd = vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath).at(0) ?? path.join(os.homedir(), "Desktop");
    this.registerKwaipilotSchema();

    // 注册监听器
    this.registerListener();

    // 注册导航命令
    this.registerNavigationCommands();

    // 监听编辑器变化事件，更新diff导航状态
    vscode.window.onDidChangeActiveTextEditor(() => {
      this.updateDiffNavigationContext();
    }, null, this.context.subscriptions);
  }

  private registerKwaipilotSchema() {
    // 创建一个可以处理 kwaipilot-diff URI 的内容提供程序
    const diffContentProvider = new (class implements vscode.TextDocumentContentProvider {
      // 存储额外的内容映射（用于特殊情况）
      private contentMap = new Map<string, string>();

      // 设置特定标识符的内容
      setContent(identifier: string, content: string): void {
        this.contentMap.set(identifier, content);
      }

      // 获取特定标识符的内容
      getContent(identifier: string): string | undefined {
        return this.contentMap.get(identifier);
      }

      // 主要的内容提供方法，优先使用 contentMap 中的内容，否则从 URI 查询参数解析
      provideTextDocumentContent(uri: vscode.Uri): string {
        // 尝试从内容映射获取
        const identifierPath = uri.path;
        if (this.contentMap.has(identifierPath)) {
          return this.contentMap.get(identifierPath) || "";
        }
        // 否则从 URI 查询参数解析
        return Buffer.from(uri.query, "base64").toString("utf-8");
      }
    })();

    // 注册内容提供程序
    this.context.subscriptions.push(vscode.workspace.registerTextDocumentContentProvider(DIFF_VIEW_URI_SCHEME, diffContentProvider));
    this.diffContentProvider = diffContentProvider;
  }

  getFileDiffState(relPath: string) {
    return this.diffState.get(relPath);
  }

  private registerListener() {
    // 打开一个 虚拟 diff 文件编辑器
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.OPEN_INMEORY_FILE_TO_DIFF_EDITOR, async (payload) => {
      if (!payload) return;
      const { originValue, modifyValue, filepath } = payload;
      const fileName = path.basename(filepath || "unnamed");

      // 创建两个虚拟URI，用于比较
      const leftUri = vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${fileName}`).with({
        query: Buffer.from(originValue || "").toString("base64"),
      });
      const rightUri = vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${DIFF_VIEW_SNAPSHOT_MODIFIED_PREFIX}${fileName}`).with({
        query: Buffer.from(modifyValue || "").toString("base64"),
      });

      // 使用vscode.diff命令打开diff编辑器
      await vscode.commands.executeCommand(
        "vscode.diff",
        leftUri,
        rightUri,
        `${fileName} ↔ ${fileName}`,
        {
          // preview: false, // 设置为false，使diff面板保持固定打开状态
          diffEditor: {
            renderSideBySide: false, // 以单列模式显示diff
          },
        },
      );
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR_MAYBE_DIFF_EDITOR, async (payload?: {
      filepath: string;
    }) => {
      if (!payload) {
        this.logger.warn("收到无效的 OPEN_FILE_TO_EDITOR_MAYBE_DIFF_EDITOR 事件", this.loggerScope);
        return;
      }
      // 先检查文件是否存在
      const absolutePath = path.resolve(this.cwd, payload.filepath);
      const fileExists = await fileExistsAtPath(absolutePath);

      if (!fileExists) {
        // 文件不存在，显示错误提示并返回
        const fileName = path.basename(payload.filepath);
        vscode.window.showErrorMessage(t("fileNotFound", { msg: fileName }));
        this.logger.warn(`文件不存在: ${payload.filepath}`, this.loggerScope);
        return;
      }
      const diffViewProvider = this.diffState.get(payload.filepath)?.diffViewProvider;
      if (!diffViewProvider) {
        // 文件存在，尝试打开
        vscode.workspace.openTextDocument(absolutePath).then((document) => {
          vscode.window.showTextDocument(document, this.viewColumn, true);
        });
        return;
      }
      const diffTab = vscode.window.tabGroups.all
        .flatMap(group => group.tabs)
        // @IMP: 需要过滤掉 diff 快照 ，因为这个快照他本身不是一个真正的文件
        .filter(tab => !(tab.input instanceof vscode.TabInputTextDiff && tab?.input?.modified?.path.startsWith(DIFF_VIEW_SNAPSHOT_MODIFIED_PREFIX)))
        .find(
          tab =>
            tab.input instanceof vscode.TabInputTextDiff
            && tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME
            && arePathsEqual(tab.input.original.fsPath, payload.filepath),
        );
      if (diffTab?.isActive) {
        return;
      }
      // 激活这个tab
      diffViewProvider.openDiffEditor(this.viewColumn, {
        openAndActiveInIde: true,
      });
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.OPEN_MULTI_DIFF_EDITOR, async (payload?: {
      diffContents: MultiDiffContent[];
      title: string;
    }) => {
      if (!payload) return;
      const { diffContents, title } = payload;

      // 检查输入参数
      if (!Array.isArray(diffContents) || diffContents.length === 0) {
        this.logger.warn("OPEN_MULTI_DIFF_EDITOR: 无效的 diffContents", this.loggerScope, {
          value: { diffContents, title },
        });
        return;
      }

      // 聚合 path 相同的 diffContents，取第一个的 originanlValue 和最后一个的 modifiedValue
      const diffContentsMap = new Map<string, { originalValue: string; modifyValue: string }>();
      diffContents.forEach((diffContent) => {
        const diffContentExist = diffContentsMap.get(diffContent.filepath);
        diffContentsMap.set(diffContent.filepath, {
          originalValue: diffContentExist ? diffContentExist?.originalValue : diffContent.originalCode,
          modifyValue: diffContent.modifiedCode,
        });
      });

      if (diffContentsMap.size === 0) {
        this.logger.warn("OPEN_MULTI_DIFF_EDITOR: 没有有效的 diffContents", this.loggerScope);
        return;
      }

      const diffContentsList = Array.from(diffContentsMap.entries());

      try {
        // 为每个文件创建虚拟 URI
        const resources = diffContentsList.map(([filepath, { originalValue, modifyValue }]) => {
          // const fileName = path.basename(filepath);
          // const relativePath = vscode.workspace.asRelativePath(filepath);
          const absolutePath = path.resolve(this.cwd, filepath);

          const originalUri = vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${filepath}`).with({
            query: Buffer.from(originalValue || "").toString("base64"),
          });

          const modifiedUri = vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${filepath}`).with({
            query: Buffer.from(modifyValue || "").toString("base64"),
          });

          return {
            originalUri,
            modifiedUri,
            // 这里也要传一个 URI
            goToFileUri: vscode.Uri.file(absolutePath),
          };
        });

        // 调用 _workbench.openMultiDiffEditor 命令
        await vscode.commands.executeCommand("_workbench.openMultiDiffEditor", {
          title: title || t("chat.reviewChanges"),
          resources,
          label: APP_DIFF_VIEW_LABEL,
          multiDiffSourceUri: vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${diffContents[0].filepath}`),
          // 下面两个参数只在IDE中可用
          editorOptions: {
            // 非 preview 模式，暂时没有支持，后面再看看
            preview: false,
            // 重启编辑器时会关闭 multiDiffEditor. 因为使用的是内存临时文件，重启会显示no changed files。
            unSerialize: true,
          },
        });
      }
      catch (error) {
        this.logger.error("打开多文件差异编辑器失败", this.loggerScope, {
          err: error,
          value: {
            diffContentsCount: diffContents.length,
            title,
            errorType: error instanceof Error ? error.constructor.name : typeof error,
            errorMessage: error instanceof Error ? error.message : String(error),
          },
        });

        const errorMessage = error instanceof Error
          ? `review changes failed: ${error.message}`
          : `review changes failed: ${String(error)}`;
        vscode.window.showErrorMessage(errorMessage);
      }
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.FILE_EDIT, async (payload?: {
      filename: string;
      modelOutput: string;
      sessionId: string;
      chatId: string;
      applyId: string;
    }) => {
      if (!payload) return undefined;
      await this.writeToFile({
        path: payload.filename,
        content: payload.modelOutput,
      }, {
        chatId: payload.chatId,
        sessionId: payload.sessionId,
        source: "markdown-body",
      });

      return undefined;
    });
  }

  /**
   * 注册导航命令
   */
  private registerNavigationCommands() {
    // 注册命令：下一个diff文件
    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.nextDiffFile`, () => {
        this.navigateToNextDiffFile("next");
      }),
    );

    // 注册命令：上一个diff文件
    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.previousDiffFile`, () => {
        this.navigateToNextDiffFile("previous");
      }),
    );
  }

  /**
   * 导航到下一个diff文件
   */
  public async navigateToNextDiffFile(type: "previous" | "next") {
    const currentEditor = vscode.window.activeTextEditor;
    const diffInfo = (currentEditor as any).diffInformation as [any];
    if (!currentEditor || !diffInfo) {
      return;
    }
    const currentFile = diffInfo[0].original?.path;
    const diffViewProvider = this.diffState.get(currentFile)?.diffViewProvider;
    if (!diffViewProvider) {
      return;
    }
    const diffFiles = Array.from(this.diffState.keys());
    const index = diffFiles.indexOf(currentFile);
    const nextDiffViewProvider = this.diffState.get(diffFiles[index + (type === "next" ? 1 : -1) % diffFiles.length])?.diffViewProvider;
    if (!nextDiffViewProvider) {
      return;
    }
    await nextDiffViewProvider.openDiffEditor(this.viewColumn, {
      openAndActiveInIde: true,
    });
  }

  async abortApply(relPath: string) {
    const state = this.state.get(relPath);
    if (!state) {
      return;
    }
    this.logger.debug(`取消${relPath}请求`, this.loggerScope);
    state.requestController?.abort();
  }

  /**
   * 根据 LLM 返回的修改文件的请求，生成最终的文件内容并写入
   * @param payload
   * @param context
   * @returns
   */
  public async writeToFile(payload: EditFileRequest, context: EditFileContext) {
    const relPath = vscode.workspace.asRelativePath(payload.path);
    await this.abortApply(relPath);

    const deferred = createDeferred<EditFileResponse>();

    const applyId = uuidv4();
    const controller = new AbortController();

    this.state.set(relPath, {
      context,
      messageQueue: undefined,
      deferred,
      requestController: controller,
      applyId,
    });

    let currentFileContent = "";
    try {
      currentFileContent = await fs.readFile(path.resolve(this.cwd, relPath), "utf-8");
    }
    catch (error) {
      // 创建文件
      this.logger.debug(`文件${relPath}不存在，将创建新文件`, this.loggerScope);
    }
    const applyBody = {
      files: [
        {
          filePath: relPath,
          fileContent: currentFileContent.toString(),
        },
      ],
      modelOutput: `${payload.content}`,
      language: payload.language,
      instruction: payload.instructions,
      applyId,
      composer: payload.composer,
      platform: PLUGIN_PLATFORM,
      username: this.getBase(GlobalStateManager).get(GlobalState.USER_INFO)?.name ?? "",
    };
    this.logger.info("开始instant-apply", this.loggerScope, {
      value: applyBody,
    });

    const MAX_WAITTING = 30 * 1000;

    let timerout: NodeJS.Timeout = setTimeout(() => {
      controller.abort();
    }, MAX_WAITTING);

    try {
      controller.signal.onabort = () => {
        this.end(relPath);
        this.logger.debug(`取消${relPath}请求`, this.loggerScope);
        // 还原 diffViewProvider 到初始状态
        setTimeout(async () => {
          await this.rejectFile({ filepaths: [relPath] });
          this.composer.updateFileStatus({ filepaths: [relPath], type: "undo" });
        }, 600);
        deferred.resolve({
          type: "failed",
          content: "取消请求",
        });
      };
      this.api.fetchEventSource("/eapi/kwaipilot/chat/instant/apply", {
        method: "POST",
        body: JSON.stringify(applyBody),
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
        },
        signal: controller.signal,
        onmessage: (event: any) => {
          clearTimeout(timerout);
          timerout = setTimeout(() => {
            controller.abort();
          }, MAX_WAITTING);

          try {
            const data = JSON.parse(event.data.toString());
            if (data.type === "content") {
              this.updateMessage(relPath, data.content);
            }
          }
          catch (error) {
            this.logger.debug("解析消息失败", this.loggerScope, { err: error });
          }
        },
        onclose: () => {
          clearTimeout(timerout);
          this.end(relPath);
          this.state.set(relPath, {
            context,
            messageQueue: undefined,
            deferred,
            requestController: null,
            applyId,
          });
        },
        onerror: (e: any) => {
          clearTimeout(timerout);
          throw Error(e);
        },
      }).catch((error) => {
        this.logger.debug("请求异常", this.loggerScope, { err: error });
        controller.abort();
        this.end(relPath);
        this.showErrorToUser({
          filePath: relPath,
          errorType: "unknown",
          message: `文件处理发生异常: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: Date.now(),
        });
        deferred.resolve({
          type: "failed",
          content: error instanceof Error ? error.message : String(error),
        });
      });
    }
    catch (error) {
      this.logger.debug("请求异常", this.loggerScope, { err: error });
      controller.abort();
      this.end(relPath);
      this.showErrorToUser({
        filePath: relPath,
        errorType: "unknown",
        message: `文件处理发生异常: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now(),
      });
      deferred.resolve({
        type: "failed",
        content: error instanceof Error ? error.message : String(error),
      });
    }

    return await deferred.promise;
  }

  /**
   * 更新最新内容，写入并生成 diff
   * 直接接受最终 content 写入到文件中，而不是通过 LLM 流式生成
   * @param payload 包含文件路径和内容的请求
   * @returns 编辑文件的响应
   */
  public async writeToFileV1(
    payload: WriteFileRequest,
  ): Promise<EditFileResponse> {
    const { path: oriPath, after, diff } = payload;
    const relPath = vscode.workspace.asRelativePath(oriPath);
    await this.abortApply(relPath);

    const data = await this.editFile({
      path: relPath,
      content: after,
      isFinal: true,
      processingMode: "full-content",
      diff,
    });
    const diffState = this.diffState.get(relPath);
    if (diffState) {
      diffState.diffViewProvider.processLineNumber = 0;
    }

    return data;
  }

  public async replaceFile(
    payload: ReplaceInFileRequest,
  ): Promise<EditFileResponse> {
    try {
      const { path: absoluteFilePath, searchReplaceInfo, sessionId, chatId } = payload;
      const normalizedPath = path.resolve(absoluteFilePath).replace(/\\/g, "/");
      const relPath = vscode.workspace.asRelativePath(normalizedPath);
      const notSearch: { hasChanged: boolean; search: string }[] = [];
      await this.abortApply(relPath);
      const oldContent = await readFile(normalizedPath, "utf-8");
      // 添加调试日志
      this.logger.info(`Original path: ${absoluteFilePath}`, this.loggerScope);
      this.logger.info(`Normalized path: ${normalizedPath}`, this.loggerScope);

      for (const s of searchReplaceInfo) {
        const replaceRelt = await replaceInFile({
          files: normalizedPath,
          from: s.search,
          to: s.replace,
        });
        if (!replaceRelt[0].hasChanged) {
          const regexp = new RegExp(
            s.search
              .replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
              .replace(/\s+/g, "\\s+")
              .replace(/["”“]/g, `["“”]`)
              .replace(/['‘’]/g, `['‘’]`),
          );
          const backupRelt = await replaceInFile({
            files: normalizedPath,
            from: regexp,
            to: s.replace,
          });
          if (!backupRelt[0].hasChanged) {
            notSearch.push({
              hasChanged: false,
              search: s.search,
            });
          }
        }
        if (notSearch.length === searchReplaceInfo.length) {
          this.logger.error("All search content returned no matches", this.loggerScope, {
            value: {
              content: oldContent,
              searchReplaceInfo,
            },
          });
          return {
            type: "failed",
            content: "All search content returned no matches",
          };
        }
      }
      let state = this.diffState.get(relPath);
      const newContent = await readFile(normalizedPath, "utf-8");
      if (state) {
        state.diffViewProvider.updateEditorView(newContent, this.viewColumn);
      }
      else {
        state = {
          context: {
            sessionId,
            chatId,
            source: "agent-v2",
          },
          applyId: "",
          diffViewProvider: new DiffViewProvider(absoluteFilePath, this.logger),
        };
        await state.diffViewProvider.openAndShowDiff(oldContent, newContent, this.viewColumn);
      }

      this.diffState.set(relPath, state);

      this.logger.info(`replace file count: ${searchReplaceInfo.length}, success: ${searchReplaceInfo.length - notSearch.length}, failed: ${notSearch.length}`, this.loggerScope);

      return {
        type: "success",
        content: notSearch.length === 0 ? "All search content replaced successfully" : `Some search content returned no matches:\n${notSearch.map((s, i) => `${i + 1}. ${s.search}`).join("\n")}`,
      };
    }
    catch (e) {
      this.logger.error("replace file error", this.loggerScope, { value: payload, err: e });

      return {
        type: "failed",
        content: e instanceof Error ? e.message : String(e),
      };
    }
  }

  private end(filepath: string) {
    const state = this.state.get(filepath);
    const messageQueue = state?.messageQueue;
    if (messageQueue) {
      this.logger.debug(`文件处理结束: ${filepath}`, this.loggerScope, {
        value: {
          queueLength: this.fileProcessingQueue.length,
          currentQueue: JSON.stringify(this.fileProcessingQueue),
        },
      });
      messageQueue.end();
    }
  }

  private updateMessage(filepath: string, message: string) {
    const state = this.state.get(filepath);
    if (!state) {
      throw new Error(`未找到${filepath}对应的state`);
    }
    if (!state.messageQueue) {
      const deferred = state.deferred;
      if (!deferred) {
        this.logger.warn(`未找到${filepath}对应的deferred对象`, this.loggerScope);
        return;
      }

      this.logger.debug(`创建新的消息队列: ${filepath}`, this.loggerScope);
      const messageQueue = new MessageQueue(filepath, this, deferred, state, () => {
        const diffState = this.diffState.get(filepath);
        if (diffState) {
          diffState.diffViewProvider.processLineNumber = 0;
        }
      });

      state.messageQueue = messageQueue;
    }
    state.messageQueue.push(message);
  }

  public async editFile(payload: WriteToFilePayload): Promise<EditFileResponse> {
    const { path: relPath, content, diff, isFinal, processingMode } = payload;
    const result: EditFileResponse = {
      type: "success",
      content: "",
    };
    const absoluteFilePath = path.resolve(this.cwd, relPath);
    let state = this.diffState.get(relPath);
    if (!state) {
      state = {
        context: {
          sessionId: "inline-chat",
          chatId: "inline-chat",
          source: "inline-chat",
        },
        applyId: "inline-chat",
        diffViewProvider: new DiffViewProvider(absoluteFilePath, this.logger),
      };
      this.diffState.set(relPath, state);
    }
    const diffViewProvider = state?.diffViewProvider;

    // 更新导航按钮状态
    this.updateDiffNavigationContext();

    let fileExists: boolean;
    let oldContent: string;
    if (diffViewProvider.editType !== undefined) {
      fileExists = diffViewProvider.editType === "modify";
    }
    else {
      const absolutePath = path.resolve(this.cwd, relPath);
      fileExists = await fileExistsAtPath(absolutePath);
      diffViewProvider.editType = fileExists ? "modify" : "create";
    }
    if (fileExists) {
      try {
        oldContent = await fs.readFile(path.resolve(this.cwd, relPath), "utf-8");
      }
      catch (error) {
        oldContent = "";
      }
    }
    else {
      oldContent = "";
    }
    try {
      let newContent: string = "";
      if (diff) {
        try {
          newContent = await this.diff.constructNewFileContent(
            diff,
            oldContent,
            isFinal ?? false,
          );
        }
        catch (error) {
          // await diffViewProvider.revertChanges(this.viewColumn);
          // await diffViewProvider.reset();
          return {
            type: "failed",
            content: JSON.stringify(serializeError(error)),
          };
        }
      }
      else if (content) {
        newContent = content;

        // pre-processing newContent for cases where weaker models might add artifacts like markdown codeblock markers (deepseek/llama) or extra escape characters (gemini)
        if (newContent && newContent.startsWith("```")) {
          // this handles cases where it includes language specifiers like ```python ```js
          newContent = newContent.split("\n").slice(1).join("\n").trim();
        }
        if (newContent && newContent.endsWith("```")) {
          newContent = newContent.split("\n").slice(0, -1).join("\n").trim();
        }
      }
      else {
        result.content = "content or diff is not ok.";
        result.type = "failed";
        return result;
      }

      newContent = newContent ? newContent.trimEnd() : ""; // remove any trailing newlines, since it's automatically inserted by the editor
      newContent = newContent ? fixModelHtmlEscaping(newContent) : "";
      newContent = newContent ? removeInvalidChars(newContent) : "";
      if (!diffViewProvider.isEditing) {
        await diffViewProvider.open(relPath, this.viewColumn);
      }
      await diffViewProvider.update(newContent, isFinal ?? false, processingMode);
    }

    catch (error) {
      // await diffViewProvider.revertChanges(this.viewColumn);
      // await diffViewProvider.reset();
      return {
        type: "failed",
        content: JSON.stringify(serializeError(error)),
      };
    }
    finally {
      if (isFinal) {
        const chatData = await this.getCurrentChatData();
        this.weblogger.$reportUserAction({
          key: "edit_file_data",
          type: fileExists ? "edit_file" : "new_file",
          agentMode: chatData?.agentMode,
          chatMode: chatData?.chatMode,
          content: JSON.stringify({
            path: relPath,
            content: oldContent,
            diff,
            ok: result.type === "success",
          }),
        });
      };
      this.logger.info(`上报埋点 ${relPath} ${result.type} ${result.content}`, this.loggerScope);
    }
    return result;
  }

  public async finishEditFileWhenEmpty({ path }: {
    path: string;
  }): Promise<EditFileResponse> {
    const diffViewProvider = this.diffState.get(path)?.diffViewProvider;

    if (!diffViewProvider) {
      this.logger.warn("收到无效的 WRITE_TO_FILE 事件", this.loggerScope, { value: path });
      return {
        type: "failed",
        content: `relPath or content or diff is not ok.`,
      };
    }
    if (!diffViewProvider.isEditing) {
      await diffViewProvider.open(path, this.viewColumn);
    }
    await diffViewProvider.update("", true);

    return {
      type: "success",
      content: `The content was successfully saved to ${path.toPosix()}.\n\n`,
    };
  }

  public async acceptFile({ filepaths, single = true, openFile = true }: { filepaths: string[]; single?: boolean; openFile?: boolean }) {
    for (const filepath of filepaths) {
      const diffViewProvider = this.diffState.get(filepath)?.diffViewProvider;
      if (diffViewProvider?.diffEnded) {
        const { keepBlocks, undoBlocks } = diffViewProvider.reportDiffContents();
        const applyId = this.state.get(filepath)?.applyId ?? "";
        await diffViewProvider.saveChanges(this.viewColumn, {
          openFile,
        });
        this.reportFileApply(
          filepath,
          {
            keep: {
              filepath,
              applyId,
              diffContent: keepBlocks,
              diffLineCount: keepBlocks.reduce((acc, cur) => acc + (cur.modifyEndLine - cur.modifyStartLine + 1), 0),
              type: single ? "single" : "all",
            },
            undo: {
              filepath,
              applyId,
              diffContent: undoBlocks,
              diffLineCount: undoBlocks.reduce((acc, cur) => acc + (cur.modifyEndLine - cur.modifyStartLine + 1), 0),
              type: "block",
            },
          },
        );
        this.diffState.delete(filepath);
      }
    }
    this.reportFileAction({
      filename: filepaths[0],
      type: single ? "single" : "all",
      action: "keep",
    });
    // 更新导航按钮状态
    this.updateDiffNavigationContext();
  }

  public async acceptAll() {
    const diffEndFiles = this.getDiffEndFiles();
    await this.acceptFile({ filepaths: diffEndFiles, single: false });
  }

  public async rejectFile({ filepaths, single = true, partialPaths }: { filepaths: string[]; single?: boolean; partialPaths?: string[] }) {
    for (const filepath of filepaths) {
      const diffViewProvider = this.diffState.get(filepath)?.diffViewProvider;
      if (diffViewProvider?.diffEnded) {
        if (!partialPaths?.includes(filepath)) {
          await diffViewProvider.revertChanges(this.viewColumn);
        }
        const applyId = this.state.get(filepath)?.applyId ?? "";
        this.reportFileApply(
          filepath,
          {
            keep: {
              filepath,
              applyId,
              diffContent: [],
              diffLineCount: 0,
              type: single ? "single" : "all",
            },
            undo: {
              filepath,
              applyId,
              diffContent: diffViewProvider.agentGenDiff,
              diffLineCount: diffViewProvider.agentGenDiff.reduce((acc, cur) => acc + (cur.modifyEndLine - cur.modifyStartLine + 1), 0),
              type: single ? "single" : "all",
            },
          },
        );
        this.diffState.delete(filepath);
      }
    }
    // 更新导航按钮状态
    this.updateDiffNavigationContext();
  }

  public async rejectAll(partialPaths?: string[]) {
    const diffEndFiles = this.getDiffEndFiles();
    await this.rejectFile({ filepaths: diffEndFiles, single: false, partialPaths });
  }

  /** IDE 内代码块拒绝 */
  public async rejectBlock({ filepath, diffBlock }: { filepath: string; diffBlock: DiffBlock[] }) {
    const applyId = this.state.get(filepath)?.applyId ?? "";
    this.reportFileApply(
      filepath,
      {
        keep: {
          filepath,
          applyId,
          diffContent: [],
          diffLineCount: 0,
          type: "single",
        },
        undo: {
          filepath,
          applyId,
          diffContent: diffBlock,
          diffLineCount: diffBlock.reduce((acc, cur) => acc + (cur.modifyEndLine - cur.modifyStartLine + 1), 0),
          type: "single",
        },
      },
    );
    const diffViewProvider = this.diffState.get(filepath)?.diffViewProvider;
    if (diffViewProvider) {
      diffViewProvider.rmDiffBlocks(diffBlock);
    }
  }

  /** IDE 内代码块接受 */
  public async acceptBlock({ filepath, diffBlock }: { filepath: string; diffBlock: DiffBlock[] }) {
    const applyId = this.state.get(filepath)?.applyId ?? "";
    this.reportFileApply(
      filepath,
      {
        keep: {
          filepath,
          applyId,
          diffContent: diffBlock,
          diffLineCount: diffBlock.reduce((acc, cur) => acc + (cur.modifyEndLine - cur.modifyStartLine + 1), 0),
          type: "single",
        },
        undo: {
          filepath,
          applyId,
          diffContent: [],
          diffLineCount: 0,
          type: "single",
        },
      },
    );
    const diffViewProvider = this.diffState.get(filepath)?.diffViewProvider;
    if (diffViewProvider) {
      diffViewProvider.rmDiffBlocks(diffBlock);
    }
  }

  private getDiffEndFiles() {
    const filepaths = Array.from(this.diffState.keys());
    return filepaths.filter((filepath) => {
      const diffViewProvider = this.diffState.get(filepath)?.diffViewProvider;
      return diffViewProvider?.diffEnded;
    });
  }

  private get bridge() {
    return this.getBase(Bridge);
  }

  private get diff() {
    return this.getCore(DiffModule);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get api() {
    return this.getBase(Api);
  }

  private get composer() {
    return this.getService(ComposerService);
  }

  /**
   * 更新diff导航按钮的上下文状态
   */
  private updateDiffNavigationContext() {
    const filepaths = Array.from(this.diffState.keys());

    vscode.commands.executeCommand("setContext", `${APP_NAME}.hasMutipleDiffFiles`, filepaths.length > 1);
  }

  /**
   * 向用户显示文件处理错误
   */
  private showErrorToUser(error: FileProcessError) {
    const existingError = this.recentErrors.get(error.filePath);
    if (existingError && existingError.errorType === error.errorType
      && (error.timestamp - existingError.timestamp) < 5000) {
      return; // 5秒内不重复显示相同类型的错误
    }

    this.recentErrors.set(error.filePath, error);

    setTimeout(() => {
      if (this.recentErrors.get(error.filePath) === error) {
        this.recentErrors.delete(error.filePath);
      }
    }, 10000); // 10秒后清理

    // 显示错误提示
    const fileName = path.basename(error.filePath);
    vscode.window.showErrorMessage(`文件 ${fileName} 处理出错: ${error.message}`);
  }

  private reportFileApply(
    filename: string,
    data: {
      keep: ReportEditFile;
      undo: ReportEditFile;
    },
  ) {
    const state = this.diffState.get(filename);
    if (!state) return;
    this.getCurrentChatData().then((chatData) => {
      data.keep.diffContent.length && this.getBase(WebloggerManager)?.$reportUserAction<"instant_apply_feedback">({
        key: "instant_apply_feedback",
        operator: "",
        content: JSON.stringify(data.keep),
        type: "accept",
        subType: "",
        applyId: data.keep.applyId,
        agentMode: chatData?.agentMode,
        chatMode: chatData?.chatMode,
      });
      data.undo.diffContent.length && this.getBase(WebloggerManager)?.$reportUserAction<"instant_apply_feedback">({
        key: "instant_apply_feedback",
        operator: "",
        content: JSON.stringify(data.undo),
        type: "reject",
        subType: "",
        applyId: data.undo.applyId,
        agentMode: chatData?.agentMode,
        chatMode: chatData?.chatMode,
      });
    });
  };

  /**
   *
   * @param param0
   */
  private reportFileAction({ filename, action, type }: { filename: string; action: "keep" | "undo"; type: "all" | "single" }) {
    const state = this.diffState.get(filename);
    if (!state) return;
    const { applyId, context: { sessionId, chatId } } = state;
    let param: ReportOpt<"agent_code_keep" | "agent_code_undo">;
    this.getCurrentChatData().then((chatData) => {
      if (action === "keep") {
        param = {
          key: "agent_code_keep",
          type,
          agentMode: chatData?.agentMode,
          chatMode: chatData?.chatMode,
        };
        this.getBase(WebloggerManager)?.$reportUserAction(param);
      }
      else {
        param = {
          key: "agent_code_undo",
          type,
          agentMode: chatData?.agentMode,
          chatMode: chatData?.chatMode,
        };
      }
      this.getBase(WebloggerManager)?.$reportUserAction({
        ...param,
        chatId,
        applyId,
        sessionId,
      });
    });
  }

  private get viewColumn() {
    return this.getBase(WorkspaceStateManager).get(WorkspaceState.FULL_MODE_NEW_WINDOW) ? vscode.ViewColumn.One : vscode.ViewColumn.Active;
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  /**
   * 获取当前的 agent 模式 和 chatMode
   */
  private async getCurrentChatData() {
    try {
      const { chatMode, agentMode } = await this.composer.$getCurrentChatData();
      return {
        chatMode,
        agentMode,
      };
    }
    catch (error) {
      this.logger.debug(`获取 agentMode / chatMode 失败 ${(error as Error).message}}`, this.loggerScope);
      return;
    }
  }
}

class MessageQueue {
  private message: string = ""; // 存储接收到的所有消息
  private _isEnd: boolean = false; // 是否结束
  aborted: boolean = false;
  private firstLine = false;
  private timer: any;

  abort() {
    this.aborted = true;
  }

  constructor(
    private filePath: string,
    private writeToFileService: WriteToFileService,
    private editorDeferred: Deferred<EditFileResponse>,
    private state: EditFileState,
    private clear: () => void,
  ) {
    const done = (res: EditFileResponse) => {
      this.clear();
      this.editorDeferred.resolve(res);
    };
    const executeLater = () => {
      clearTimeout(this.timer);
      this.timer = setTimeout(async () => {
        const { content, isEnd } = this.getFullContent();
        if (content) {
          try {
            const res = await this.writeToFileService.editFile({
              path: this.filePath,
              content: content,
              isFinal: isEnd,
            });
            if (isEnd) {
              done(res);
            }
            else {
              executeLater();
            }
          }
          catch (e) {
            done({
              type: "failed",
              content: `editFile execution failed ${String(e)} path:${this.filePath.toPosix()}.\n\n`,
            });
            return;
          }
        }
        else if (isEnd) {
          const res = await this.writeToFileService.finishEditFileWhenEmpty({
            path: this.filePath,
          });
          done(res);
        }
        else {
          executeLater();
        }
      }, 400);
    };

    executeLater();
  }

  private async writeToFile(content: string) {
    return await this.writeToFileService.editFile({
      path: this.filePath,
      content: content,
      isFinal: this._isEnd,
    });
  }

  push(message: string): void {
    // 添加消息到数组
    this.message += message;
  }

  async end() {
    this.message += "IrMOxqB41rxZRyrKzGepm";
  }

  /**
   * 获取完整的消息内容
   */
  private getFullContent(): {
    content: string;
    isEnd: boolean;
  } {
    if (this.message.includes("IrMOxqB41rxZRyrKzGepm")) {
      return {
        content: this.message.split("\n").slice(0, -1).join("\n"),
        isEnd: true,
      };
    }
    if (!this.message || this.message.trim().length === 0 || !this.message.includes("\n")) {
      return {
        content: "",
        isEnd: false,
      };
    }
    const lines = this.message.split("\n");
    let startIndex = 0;
    if (!this.firstLine) {
      startIndex = 1;
      this.firstLine = true;
    };
    this.message = lines[lines.length - 1];
    return {
      content: lines.slice(startIndex, -1).join("\n"),
      isEnd: this.message.includes("IrMOxqB41rxZRyrKzGepm"),
    };
  }

  /**
   * 获取文件路径
   */
  get filename(): string {
    return this.filePath;
  }
}
