import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import {
  Config,
  FIGMA_REDIRECT_URI_VSCODE,
  FIGMA_REDIRECT_URI_KWAIPILOT,
} from "../../base/state-manager/types";
import { SettingPanelModule } from "../../core/setting-panel";
import { ConfigManager } from "../../base/state-manager";
import { t } from "i18next";
import { KwaipilotEnv } from "../../const";
import { WebloggerManager } from "../../base/weblogger";
import { APP_NAME, IS_EXTERNAL } from "shared/lib/const";
import { Api } from "../../base/http-client";

export class URIHandlerService extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    this.registerURIHandler();
  }

  private get httpClient() {
    return this.getBase(Api);
  }

  private registerURIHandler() {
    vscode.window.registerUriHandler({
      handleUri: (uri) => {
        this.handleKwaipilotUri(uri);
      },
    });
  }

  private handleKwaipilotUri(uri: vscode.Uri) {
    // 解析URI参数
    const params = new URLSearchParams(uri.query);
    console.log("handleKwaipilotUri", uri, uri.path);

    // 根据路径执行不同的逻辑
    switch (uri.path) {
      case "/mcp-management":
        vscode.commands.executeCommand(`${APP_NAME}.openMCPManagement`, uri.query);
        break;

      case "/figmaToken":
        console.log("命中Figma uri", uri, uri.path);
        this.saveFigmaToken(params);
        this.weblogger.$reportUserAction({
          key: "figma_oauth_succ",
          type: undefined,
        });
        break;
      case "/vercelToken":
        console.log("命中Vercel uri", uri, uri.path);
        this.handleVerceOauthSuccess();
        break;

      default:
        console.log(`未处理的URI路径: ${uri.path}`);
        break;
    }
  }

  // 处理vercel 授权成功
  private async handleVerceOauthSuccess() {
    this.weblogger.$reportUserAction({
      key: "vercel_oauth_token_succ",
      type: undefined,
    });
    vscode.window.showInformationMessage(t("vercelTokenSaved"));
  }

  private async saveFigmaToken(params: URLSearchParams) {
    // 请求figma接口https://api.figma.com/v1/oauth/token

    console.log("开始处理figmaToken", params.get("code"));

    this.httpClient.figmaOAuthToken({
      code: params.get("code") || "",
      redirect_uri: KwaipilotEnv.isInIde ? FIGMA_REDIRECT_URI_KWAIPILOT : FIGMA_REDIRECT_URI_VSCODE,
      grant_type: "authorization_code",
      environment: IS_EXTERNAL ? "outer" : "inner",
    }).then((resp) => {
      const respData = resp.data;
      console.log("获取Figma Token 成功", respData);
      console.log("获取Figma Token 成功 access_token", respData.access_token);
      if (respData.access_token) {
        // 为什么调用/v1/me报{status: 403, err: 'Invalid token'}呢？？？？
        fetch("https://api.figma.com/v1/me", {
          method: "GET", // 默认值，可省略
          headers: {
            Authorization: "Bearer " + respData.access_token,
          },
        }).then(res => res.json()).then((res) => {
          console.log("获取Figma Email 成功", res.email);
          settingPanel.$updateSetting(Config.FIGMA_EMAIL, res.email);
          this.weblogger.$reportUserAction({
            key: "figma_oauth_email_succ",
            type: undefined,
          });
        });

        const settingPanel = this.getCore(SettingPanelModule);

        // 保存 access token
        settingPanel.$updateSetting(Config.FIGMA_TOKEN, respData.access_token);

        // 保存 refresh token（如果有）
        if (respData.refresh_token) {
          settingPanel.$updateSetting(Config.FIGMA_REFRESH_TOKEN, respData.refresh_token);
        }

        // 计算并保存过期时间（当前时间 + expires_in 秒数）
        if (respData.expires_in) {
          const expireTimestamp = Date.now() + respData.expires_in * 1000;
          settingPanel.$updateSetting(Config.FIGMA_TOKEN_EXPIRE, expireTimestamp);
        }

        vscode.window.showInformationMessage(t("figmaTokenSaved"));
      }
      else {
        vscode.window.showErrorMessage(t("figmaTokenFailed", { msg: resp.message || t("figmaTokenUnknownError") }));
      }
    }).catch((err) => {
      console.error("Figma OAuth error:", err);
      vscode.window.showErrorMessage(t("figmaOAuthFailed", { msg: err.message }));
    });
  }

  private get config() {
    return this.getBase(ConfigManager);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  /**
   * 获取当前插件运行环境
   * @returns 运行环境信息对象
   */
  public getCurrentEnvironment() {
    return {
      isInKwaipilotIDE: KwaipilotEnv.isInIde,
      isInVSCode: !KwaipilotEnv.isInIde,
      environmentName: KwaipilotEnv.isInIde ? `${APP_NAME}-ide` : "vscode",
    };
  }
}
