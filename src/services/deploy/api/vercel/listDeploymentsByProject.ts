import fetch from "node-fetch";

export interface ListDeploymentsByProjectParams {
  projectId: string;
  token: string;
  teamId?: string;
  limit?: number;
}
export interface ListDeploymentsByProjectResult {
  ok: boolean;
  id?: string;
  // url?: string;
  readyState?: string;
  raw?: any;
  message?: string;
  status?: number;
}

export async function listDeploymentsByProject(params: ListDeploymentsByProjectParams): Promise<ListDeploymentsByProjectResult> {
  const { projectId, token, teamId, limit = 1 } = params;
  try {
    const q: string[] = [`projectId=${encodeURIComponent(projectId)}`, `limit=${limit}`];
    if (teamId) q.push(`teamId=${encodeURIComponent(teamId)}`);
    const url = `https://api.vercel.com/v6/deployments?${q.join("&")}`;
    const res = await fetch(url, { headers: { Authorization: `Bearer ${token}` } });
    const json = await res.json();
    if (!res.ok) return { ok: false, message: json?.error?.message || json?.message || `HTTP ${res.status}`, raw: json, status: res.status };
    const item = Array.isArray(json?.deployments) ? json.deployments[0] : undefined;
    if (!item) return { ok: true, status: res.status };
    const ready = item?.readyState || item?.state;
    // const urlStr = item?.url ? (item.url.startsWith("http") ? item.url : `https://${item.url}`) : undefined;
    return { ok: true, id: item.uid || item.id, readyState: ready, raw: item, status: res.status };
  }
  catch (e: any) {
    return { ok: false, message: e?.message || String(e) };
  }
}
