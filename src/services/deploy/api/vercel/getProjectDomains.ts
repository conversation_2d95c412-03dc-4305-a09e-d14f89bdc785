import fetch from "node-fetch";

export interface GetProjectDomainsParams {
  idOrName: string;
  token: string;
  teamId?: string;
}

export interface DomainVerificationChallenge {
  type: string;
  domain: string;
  value: string;
  reason: string;
}

export interface ProjectDomainItem {
  name: string;
  apexName: string;
  projectId: string;
  updatedAt: number;
  createdAt: number;
  verified: boolean;
}

export interface PaginationInfo {
  count: number;
  next: number | null;
  prev: number | null;
}

export interface GetProjectDomainsResult {
  ok: boolean;
  domains?: ProjectDomainItem[];
  pagination?: PaginationInfo;
  url?: string; // convenience field: first domain name as https URL
  raw?: any;
  message?: string;
  status?: number;
}

export async function getProjectDomains(params: GetProjectDomainsParams): Promise<GetProjectDomainsResult> {
  const { idOrName, token, teamId } = params;
  try {
    const q: string[] = [];
    q.push(`target=production`);
    if (teamId) q.push(`teamId=${encodeURIComponent(teamId)}`);
    const query = q.length ? `?${q.join("&")}` : "";

    const url = `https://api.vercel.com/v9/projects/${encodeURIComponent(idOrName)}/domains${query}`;
    const res = await fetch(url, { headers: { Authorization: `Bearer ${token}` } });
    const text = await res.text();
    let json: any;
    try {
      json = JSON.parse(text);
    }
    catch {
      json = text;
    }
    if (!res.ok) {
      const message = typeof json === "string" ? json : (json?.error?.message || json?.message || `HTTP ${res.status}`);
      return { ok: false, message, raw: json, status: res.status };
    }
    const domains: ProjectDomainItem[] = Array.isArray(json?.domains)
      ? json.domains.map((d: any) => ({
          name: d?.name,
          apexName: d?.apexName,
          projectId: d?.projectId,
          updatedAt: d?.updatedAt,
          createdAt: d?.createdAt,
          verified: !!d?.verified,
        }))
      : [];
    let pagination: PaginationInfo | undefined = undefined;
    if (json?.pagination) {
      pagination = {
        count: Number(json.pagination.count) || 0,
        next: json.pagination.next ?? null,
        prev: json.pagination.prev ?? null,
      };
    }
    const firstName = domains?.[0]?.name;
    const firstUrl = firstName ? (firstName.startsWith("http") ? firstName : `https://${firstName}`) : undefined;
    return { ok: true, domains, pagination, url: firstUrl, raw: json, status: res.status };
  }
  catch (e: any) {
    return { ok: false, message: e?.message || String(e) };
  }
}
