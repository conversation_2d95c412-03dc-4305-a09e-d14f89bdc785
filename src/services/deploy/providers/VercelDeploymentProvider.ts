import * as fs from "fs";
import * as path from "path";
import * as vscode from "vscode";
import { collectFiles } from "../utils/fileCollector";
import { getUser, uploadFileWithDigest, createDeployment, getDeploymentStatus, getDeploymentLogs,
  listDeploymentsByProject, cancelDeployment, getProjectDomains } from "../api";
import { getVercelProjectId, getVercelTeamId, writeVercelConfig, readVercelConfig } from "../config/vercel";
import { ContextManager } from "../../../base/context-manager";
import { ConfigManager } from "../../../base/state-manager";
import { Config } from "../../../base/state-manager/types";
import { APP_NAME } from "shared/lib/const";
import { generateVercelProjectName } from "../utils/generateVercelProjectName";
import { t } from "i18next";
import { SettingPanelModule } from "../../../core/setting-panel";

export type DeploymentPhase = "UPLOADING" | "QUEUED" | "BUILDING" | "READY" | "ERROR";

export interface DeployResult {
  ok: boolean;
  url?: string;
  id?: string;
  account?: string;
  message?: string;
  status?: number;
}

export interface DeployParams {
  rootDir: string;
  token: string;
  teamId?: string;
  accountName?: string;
}

enum DeployInternalError {
  COMMON_ERROR_TITLE = "vercel.deploy.deployFailed", // 通用错误标题: 部署失败
  NO_FILES_TO_DEPLOY = "vercel.deploy.noFilesToDeploy", // 没有文件可以部署
  STATUS_QUERY_FAILED = "vercel.deploy.statusQueryFailed", // 状态查询失败
  UNKNOWN_ERROR = "vercel.deploy.unknownError", // 未知错误
  FILE_UPLOAD_FAILED = "vercel.deploy.fileUploadFailed", // 文件上传失败
  CANCELED = "vercel.deploy.canceled", // 部署取消
}

enum DeployInternalEvent {
  NO_WORKSPACE = "no_workspace",
  DEPLOYMENT_CREATED = "deployment_created",
  PHASE = "phase",
  DEPLOYMENT_CONTEXT = "deployment_context",
  ACCOUNT_INFO = "account_info",
  RESULT_ERROR = "result_error",
  RESULT_SUCCESS = "result_success",
  AUTH_REQUIRED = "auth_required",
  UPLOAD_START = "upload_start",
  UPLOAD_PROGRESS = "upload_progress",
  UPLOAD_DONE = "upload_done",
}

// 统一的内部事件负载类型，使用 kind 作为判别字段
export type DeployInternalEventPayload =
  // 没有项目
  | ({ kind: DeployInternalEvent.NO_WORKSPACE })
  // 部署创建
  | ({ kind: DeployInternalEvent.DEPLOYMENT_CREATED; url?: string })
  // 部署状态
  | ({ kind: DeployInternalEvent.PHASE; phase: DeploymentPhase })
  // 部署上下文（项目/团队信息等）
  | ({ kind: DeployInternalEvent.DEPLOYMENT_CONTEXT; id?: string; teamId?: string; teamName?: string; projectId?: string; projectName?: string; createdAt?: number })
  // 账号信息
  | ({ kind: DeployInternalEvent.ACCOUNT_INFO; account: string })
  // 错误/成功结果
  | ({ kind: DeployInternalEvent.RESULT_ERROR; message: string; errorLogs?: string; canceledAt?: number })
  | ({ kind: DeployInternalEvent.RESULT_SUCCESS; url: string })
  // 需要认证
  | ({ kind: DeployInternalEvent.AUTH_REQUIRED })
  // 上传进度
  | ({ kind: DeployInternalEvent.UPLOAD_START; total: number })
  | ({ kind: DeployInternalEvent.UPLOAD_PROGRESS; uploaded: number; total: number })
  | ({ kind: DeployInternalEvent.UPLOAD_DONE });

export class VercelDeploymentProvider {
  private emit: (payload: DeployInternalEventPayload) => void;
  private pollTimer: NodeJS.Timeout | null = null;
  private currentPollingId: string | null = null;
  private cancelRequested: boolean = false;
  private currentCreateAbortController: AbortController | null = null;
  private currentUploadAbortController: AbortController | null = null;
  // private static readonly VERCEL_TEAM_ID_FALLBACK = "team_AMxywIZD1lpskDxvZo7uuQo1";
  private ctx: ContextManager;

  constructor(opts: { emit: (payload: any) => void; ctx: ContextManager }) {
    this.emit = opts.emit;
    this.ctx = opts.ctx;
  }

  private get config(): ConfigManager {
    return this.ctx.getBase(ConfigManager);
  }

  // 统一工具函数：处理 403
  private isForbidden(res: any): boolean {
    return !res?.ok && (res as any)?.status === 403;
  }

  private handleForbiddenCase(res: { ok: boolean; status?: number; message?: string } | any): any {
    try {
      this.emit({ kind: DeployInternalEvent.AUTH_REQUIRED });
      // 清空配置
      const settingPanel = this.ctx.getCore(SettingPanelModule);
      settingPanel.$updateSetting(Config.VERCEL_EMAIL, "");
      settingPanel.$updateSetting(Config.VERCEL_TOKEN_EXPIRE, "");
      settingPanel.$updateSetting(Config.VERCEL_REFRESH_TOKEN, "");
      settingPanel.$updateSetting(Config.VERCEL_TOKEN, "");
    }
    catch (e) {
      console.error("[deploy] emit AUTH_REQUIRED failed", e);
    }
    return { ok: false, message: (res as any)?.message, status: (res as any)?.status } as any;
  }

  // 每次打开面板时，查询最近一次部署
  async queryLatest(): Promise<any> {
    const root = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!root) {
      try {
        this.emit({ kind: DeployInternalEvent.NO_WORKSPACE });
      }
      catch (e) {
        console.error("[deploy] emit no_workspace failed", e);
      }
      return { ok: true, noWorkspace: true } as any;
    }
    const newProjectName = generateVercelProjectName(APP_NAME, root);
    const configuredProjectId = await getVercelProjectId();
    // 如果配置不存在 projectId，则不进行查询
    if (!configuredProjectId) return { ok: true } as any;
    const token = this.config.get(Config.VERCEL_TOKEN);
    // 如果配置不存在 token，则不进行查询
    if (!token) return { ok: true } as any;
    let teamId = await getVercelTeamId();
    if (!teamId) {
      // 尝试通过 /user 拿默认 teamId
      const u = await getUser({ token });
      if (this.isForbidden(u)) {
        return this.handleForbiddenCase(u);
      }
      if (u.ok && u.defaultTeamId) {
        teamId = u.defaultTeamId;
        try {
          await writeVercelConfig({ teamId });
        }
        catch (e) {
          console.error("[deploy] VercelDeploymentProvider queryLatest error", e);
        }
      }
    }
    const latest = await listDeploymentsByProject({ projectId: configuredProjectId, token, teamId, limit: 1 });
    if (this.isForbidden(latest)) {
      return this.handleForbiddenCase(latest);
    }
    if (latest.ok && (latest.id)) {
      // 使用项目生产域名（而非一次性部署 URL）作为首屏展示
      try {
        if (configuredProjectId) {
          const dom = await getProjectDomains({ idOrName: configuredProjectId, token, teamId });
          if (this.isForbidden(dom)) {
            return this.handleForbiddenCase(dom as any);
          }
          const urlForUi = dom?.ok && dom?.url ? dom.url : undefined;
          if (urlForUi) this.emit({ kind: DeployInternalEvent.DEPLOYMENT_CREATED, url: urlForUi });
        }
      }
      catch (e) {
        console.error("[deploy] queryLatest getProjectDomains error", e);
      }
      const phase = this.mapReadyStateToPhase(latest.readyState);
      if (phase) this.emit({ kind: DeployInternalEvent.PHASE, phase });
      const rsUpper = (latest.readyState || "").toUpperCase();
      const isCanceled = rsUpper === "CANCELED" || rsUpper === "CANCELLED";
      try {
        const cfg = await readVercelConfig();
        const projectName = cfg?.projectName || newProjectName;
        const createdAtFromList = (latest as any)?.raw?.createdAt || (latest as any)?.raw?.created;
        if (latest.id || configuredProjectId || teamId || projectName) {
          this.emit({ kind: DeployInternalEvent.DEPLOYMENT_CONTEXT, id: latest.id, teamId, projectId: configuredProjectId, projectName, createdAt: createdAtFromList });
        }
      }
      catch (e) {
        console.error("[deploy] VercelDeploymentProvider queryLatest error", e);
      }

      // 下发账号信息，保证首开弹窗也能展示用户名称
      try {
        const u2 = await getUser({ token });
        if (this.isForbidden(u2)) {
          this.handleForbiddenCase(u2);
        }
        const accountName2 = (u2 as any)?.username || (u2 as any)?.name;
        if (u2.ok && accountName2) {
          this.emit({ kind: DeployInternalEvent.ACCOUNT_INFO, account: accountName2 });
        }
      }
      catch (e) {
        console.error("[deploy] VercelDeploymentProvider queryLatest account_info error", e);
      }
      // 若最近一次部署已是错误态
      if (phase === "ERROR" && latest.id) {
        if (isCanceled) {
          // 已取消：直接下发“已取消”文案，避免拉取错误日志
          this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: t(DeployInternalError.CANCELED) });
        }
        else { // 未取消：拉取错误日志并下发
          try {
            const status = await getDeploymentStatus({ id: latest.id, token, teamId });
            if (this.isForbidden(status)) {
              return this.handleForbiddenCase(status);
            }
            const cfg = await readVercelConfig();
            const projectId = cfg?.projectId;
            let errorLogs: string | undefined = undefined;
            if (projectId) {
              const logs = await getDeploymentLogs({ projectId, deploymentId: latest.id, token, teamId });
              if (this.isForbidden(logs)) {
                return this.handleForbiddenCase(logs);
              }
              if (logs.ok && Array.isArray(logs.events)) {
                const errorTexts = logs.events
                  .filter((ev: any) => ev && typeof ev === "object" && ev.level === "error" && typeof ev.text === "string")
                  .map((ev: any) => ev.text as string);
                if (errorTexts.length > 0) {
                  errorLogs = errorTexts.join("\n");
                }
              }
            }
            this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: (status as any)?.message || t(DeployInternalError.COMMON_ERROR_TITLE), errorLogs });
          }
          catch (e) {
            console.error("[deploy] VercelDeploymentProvider queryLatest fetch error logs error", e);
            this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: t(DeployInternalError.COMMON_ERROR_TITLE) });
          }
        }
      }
      if (latest.id && phase && phase !== "READY" && phase !== "ERROR") {
        this.startPolling(latest.id, { token, teamId });
      }
    }
    try {
      const cfg = await readVercelConfig();
      const projectName = cfg?.projectName || newProjectName;
      return { ...latest, teamId, projectId: configuredProjectId, projectName };
    }
    catch (e) {
      console.error("[deploy] VercelDeploymentProvider queryLatest error", e);
      return latest;
    }
  }

  stopPolling() {
    if (this.pollTimer) {
      clearInterval(this.pollTimer);
      this.pollTimer = null;
    }
    this.currentPollingId = null;
  }

  /** 取消当前操作：
   * - 若在本地上传/创建阶段：中止在途请求并标记取消
   * - 若已有 deploymentId 正在排队/构建：调用远端取消，并停止轮询
   */
  async cancelCurrentOperation() {
    this.cancelRequested = true;
    if (this.currentCreateAbortController) this.currentCreateAbortController.abort();
    if (this.currentUploadAbortController) this.currentUploadAbortController.abort();
    try {
      const token = this.config.get(Config.VERCEL_TOKEN);
      if (token && this.currentPollingId) {
        const teamId = await getVercelTeamId();
        const res = await cancelDeployment({ id: this.currentPollingId, token, teamId });
        if (this.isForbidden(res)) {
          this.handleForbiddenCase(res);
          return;
        }
        if (!res.ok) {
          this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: (res as any)?.message || t(DeployInternalError.COMMON_ERROR_TITLE) });
          return;
        }
        this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: t(DeployInternalError.CANCELED), canceledAt: Date.now() });
        return;
      }
      // 本地中断（上传/创建阶段）
      this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: t(DeployInternalError.CANCELED), canceledAt: Date.now() });
    }
    finally {
      this.stopPolling();
    }
  }

  startPolling(id: string, params: { token: string; teamId?: string }) {
    if (this.cancelRequested) {
      this.stopPolling();
      return;
    }
    const { token, teamId } = params;
    if (this.pollTimer) {
      if (this.currentPollingId === id) return;
      this.stopPolling();
    }
    this.currentPollingId = id;
    let lastPhase: DeploymentPhase | undefined;
    const tick = async () => {
      try {
        // 若已请求取消，立即停止轮询
        if (this.cancelRequested) {
          this.stopPolling();
          return;
        }
        if (!this.currentPollingId || this.currentPollingId !== id) return;
        const status = await getDeploymentStatus({ id, token, teamId });
        if (this.isForbidden(status)) {
          this.stopPolling();
          this.handleForbiddenCase(status);
          return;
        }
        if (!status.ok) {
          this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: status.message || t(DeployInternalError.STATUS_QUERY_FAILED) });
          this.stopPolling();
          return;
        }
        const phase = this.mapReadyStateToPhase(status.readyState);
        if (phase && phase !== lastPhase) {
          this.emit({ kind: DeployInternalEvent.PHASE, phase });
          lastPhase = phase;
        }
        if (phase === "READY") {
          let finalUrl = "";
          try {
            const cfg = await readVercelConfig();
            const projectId = cfg?.projectId;
            if (projectId) {
              const dom = await getProjectDomains({ idOrName: projectId, token, teamId });
              if (!this.isForbidden(dom) && dom?.ok && dom?.url) {
                finalUrl = dom.url;
              }
            }
          }
          catch (e) {
            // noop
          }
          this.emit({ kind: DeployInternalEvent.RESULT_SUCCESS, url: finalUrl });
          this.stopPolling();
          return;
        }
        if (phase === "ERROR") {
          // 若为取消，直接提示“已取消”，无需拉错误日志
          const rsUpper = (status.readyState || "").toUpperCase();
          const isCanceled = rsUpper === "CANCELED" || rsUpper === "CANCELLED";
          if (isCanceled) {
            this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: t(DeployInternalError.CANCELED) });
            this.stopPolling();
            return;
          }
          const cfg = await readVercelConfig();
          const projectId = cfg?.projectId;
          const logs = projectId
            ? await getDeploymentLogs({ projectId, deploymentId: id, token, teamId })
            : { ok: false, message: "missing projectId" } as any;
          if (this.isForbidden(logs)) {
            this.stopPolling();
            this.handleForbiddenCase(logs);
            return;
          }
          let errorLogs: string | undefined = undefined;
          if (logs.ok && Array.isArray(logs.events)) {
            const errorTexts = logs.events
              .filter((ev: any) => ev && typeof ev === "object" && ev.level === "error" && typeof ev.text === "string")
              .map((ev: any) => ev.text as string);
            if (errorTexts.length > 0) {
              errorLogs = errorTexts.join("\n");
            }
          }
          this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: status.message || t(DeployInternalError.COMMON_ERROR_TITLE), errorLogs });
          this.stopPolling();
        }
      }
      catch (e: any) {
        this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: e?.message || String(e) });
        this.stopPolling();
      }
    };
    this.pollTimer = setInterval(tick, 2000);
  }

  async deploy(params?: Partial<DeployParams>): Promise<DeployResult> {
    // reset cancel flags/controllers for a new deployment attempt
    // 停止上一轮轮询，避免旧部署的 ERROR 事件干扰新一轮 UI（抖动）
    this.stopPolling();
    this.cancelRequested = false;
    this.currentCreateAbortController = null;
    this.currentUploadAbortController = null;
    const root = params?.rootDir || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!root) {
      vscode.window.showErrorMessage(t("vercel.deploy.noWorkspace"));
      try {
        this.emit({ kind: DeployInternalEvent.NO_WORKSPACE });
      }
      catch (e) {
        console.error("[deploy] emit no_workspace failed", e);
      }
      return { ok: false, message: "no workspace" };
    }

    const token = this.config.get(Config.VERCEL_TOKEN);
    if (!token) {
      this.emit({ kind: DeployInternalEvent.AUTH_REQUIRED });
      return { ok: false, message: "no token" } as any;
    }

    // 预检：如果配置存在 projectId，查询最近一次部署状态（仅日志）
    try {
      const configuredProjectId = await getVercelProjectId();
      const configuredTeamIdForQuery = await getVercelTeamId();
      if (configuredProjectId) {
        const latest = await listDeploymentsByProject({ projectId: configuredProjectId, token, teamId: configuredTeamIdForQuery });
        if (this.isForbidden(latest)) {
          return this.handleForbiddenCase(latest);
        }
      }
    }
    catch {
      // 忽略预检异常
    }

    const configuredTeamId = await getVercelTeamId();
    let teamIdToUse = params?.teamId || configuredTeamId;
    // 始终获取一次用户信息：拿 accountName；若缺 teamId 则用默认 teamId
    let accountName: string | undefined;
    const u = await getUser({ token });
    if (this.isForbidden(u)) {
      return this.handleForbiddenCase(u);
    }
    if (u.ok) {
      accountName = u.username || u.name;
      if (!teamIdToUse && u.defaultTeamId) {
        teamIdToUse = u.defaultTeamId;
        try {
          await writeVercelConfig({ teamId: teamIdToUse });
        }
        catch (e) {
          console.error("[deploy] VercelDeploymentProvider deploy error", e);
        }
      }
    }

    if (this.cancelRequested) return { ok: false, message: "canceled" } as any;
    const res = await this.deployCore({ rootDir: root, token, teamId: teamIdToUse, accountName });
    if (res.ok) {
      if (res.id) this.startPolling(res.id, { token, teamId: teamIdToUse });
    }
    else if (res.message === t(DeployInternalError.NO_FILES_TO_DEPLOY)) {
      this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: t(DeployInternalError.COMMON_ERROR_TITLE), errorLogs: t(DeployInternalError.NO_FILES_TO_DEPLOY) });
    }
    else {
      const isMissingFiles = /missing\s*files/i.test(res.message || "");
      if (isMissingFiles) {
        // 由 deployCore 处理缺失文件后的重试
      }
      else {
        vscode.window.showErrorMessage(`${t(DeployInternalError.COMMON_ERROR_TITLE)}：${res.message || t(DeployInternalError.UNKNOWN_ERROR)}`);
      }
    }
    return res;
  }

  private async deployCore(params: DeployParams): Promise<DeployResult> {
    const { rootDir, token, teamId, accountName: accountNameFromParams } = params;
    const newProjectName = generateVercelProjectName(APP_NAME, rootDir);
    // 账号信息：由上层传入，避免重复拉取
    const accountName = accountNameFromParams;

    let files = collectFiles(rootDir);
    // 若筛选后的文件列表非空且不存在 vercel.json，则显式创建，并重新收集
    if (files.length > 0 && !files.some(f => f.file === "vercel.json")) {
      try {
        const vercelJsonPath = path.join(rootDir, "vercel.json");
        if (!fs.existsSync(vercelJsonPath)) {
          const vercelJsonContent = "{\"rewrites\":[{\"source\":\"/(.*)\",\"destination\":\"/index.html\"}]}";
          fs.writeFileSync(vercelJsonPath, vercelJsonContent, "utf8");
        }
        // 重新收集，严格遵循忽略与筛选规则
        files = collectFiles(rootDir);
      }
      catch (e) {
        // 若写入失败，不影响后续流程
      }
    }

    const { createHash } = await import("crypto");
    const manifests = files.map((file) => {
      const fileContent = fs.readFileSync(file.absPath);
      const fileSize = file.size;
      const sha1 = createHash("sha1").update(fileContent).digest("hex");
      return { file: file.file, size: fileSize, sha1, absPath: file.absPath } as const;
    });

    if (manifests.length === 0) {
      return { ok: false, message: t(DeployInternalError.NO_FILES_TO_DEPLOY) };
    }

    const createReq = async () => {
      const body = {
        name: newProjectName,
        target: "production" as const,
        files: manifests.map(f => ({ file: f.file, sha: f.sha1, size: f.size })),
        projectSettings: { framework: null, buildCommand: null, installCommand: null, outputDirectory: null, devCommand: null, rootDirectory: null },
      };
      if (this.cancelRequested) return { ok: false, message: "canceled" } as any;
      this.currentCreateAbortController = new AbortController();
      const res = await createDeployment({ token, teamId, body, signal: this.currentCreateAbortController.signal as any });
      this.currentCreateAbortController = null;
      if (this.isForbidden(res)) {
        return this.handleForbiddenCase(res);
      }
      if (res.ok) {
        // 一旦拿到 id，立刻记录，便于用户此刻点击“取消”能远端取消
        try {
          if ((res as any)?.id) this.currentPollingId = (res as any).id as string;
        }
        catch (e) {
          // noop
        }

        // 若在 create 返回这一刻已收到取消请求，则直接远端取消并终止后续 emit
        if (this.cancelRequested) {
          try {
            const teamIdForCancel = await getVercelTeamId();
            await cancelDeployment({ id: (res as any).id, token, teamId: teamIdForCancel });
          }
          catch (e) {
            // noop
          }
          this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: t(DeployInternalError.CANCELED) });
          return { ok: false, message: "canceled" } as any;
        }

        if (res.url) this.emit({ kind: DeployInternalEvent.DEPLOYMENT_CREATED, url: "" });
        if (accountName) this.emit({ kind: DeployInternalEvent.ACCOUNT_INFO, account: accountName });
        this.emit({ kind: DeployInternalEvent.PHASE, phase: "QUEUED" });
        try {
          const projectId = (res as any)?.raw?.projectId || (await getVercelProjectId());
          const teamObj = (res as any)?.raw?.team;
          const teamNameFromRes = teamObj?.slug;
          const teamIdFromRes = teamObj?.id;
          this.emit({ kind: DeployInternalEvent.DEPLOYMENT_CONTEXT, id: res.id, teamId: teamIdFromRes || teamId, teamName: teamNameFromRes, projectId, projectName: newProjectName });
        }
        catch (e) {
          console.error("[deploy] VercelDeploymentProvider deployCore error", e);
        }
        // 写入必要的配置，便于后续查询最新状态
        try {
          await writeVercelConfig({
            teamId: teamId || undefined,
            // projectId 需要从 res.raw.projectId 提取（若有），否则保留已有
            projectId: (res as any)?.raw?.projectId || (await getVercelProjectId()),
            projectName: newProjectName,
          });
        }
        catch (e) {
          console.error("[deploy] VercelDeploymentProvider deployCore error", e);
        }
        return { ok: true, url: res.url, id: res.id, account: accountName };
      }
      return { ok: false, message: res.message, ...(res as any) } as any;
    };

    const first = await createReq();
    if (first.ok) return first as DeployResult;

    // 处理缺失文件
    const missingRaw = (first as any).missing as Array<{ sha?: string } | string> | undefined;
    let missingList: string[] | undefined;
    if (Array.isArray(missingRaw) && missingRaw.length > 0) {
      const firstItem = missingRaw[0];
      if (typeof firstItem === "string") missingList = missingRaw as string[];
      else if (typeof firstItem === "object" && firstItem) missingList = (missingRaw as Array<any>).map(it => it.sha || it.digest || it.hash).filter(Boolean);
    }
    if (!missingList || missingList.length === 0) {
      const message = (first as any)?.message || "";
      const code = (first as any)?.code || (first as any)?.error?.code;
      const shouldFallbackUploadAll = /missing\s*files/i.test(message) || code === "missing_files";
      if (shouldFallbackUploadAll) missingList = manifests.map(m => m.sha1);
      else return { ok: false, message: (first as any)?.message };
    }

    const shaToManifest = new Map(manifests.map(m => [m.sha1, m] as const));
    if (this.cancelRequested) return { ok: false, message: "canceled" } as any;
    this.emit({ kind: DeployInternalEvent.UPLOAD_START, total: missingList.length });
    let uploadedCount = 0;
    for (const sha of missingList) {
      if (this.cancelRequested) return { ok: false, message: "canceled" } as any;
      const mf = shaToManifest.get(sha);
      if (!mf) continue;
      const buf = fs.readFileSync(mf.absPath);
      this.currentUploadAbortController = new AbortController();
      const up = await uploadFileWithDigest({ token, teamId, content: buf, sha: mf.sha1, size: mf.size, signal: this.currentUploadAbortController.signal as any });
      this.currentUploadAbortController = null;
      if (this.cancelRequested) return { ok: false, message: "canceled" } as any;
      if (!up.ok) {
        if (this.isForbidden(up)) this.handleForbiddenCase(up);
        return { ok: false, message: t(DeployInternalError.FILE_UPLOAD_FAILED, { file: mf.file, status: up.status }), status: (up as any)?.status } as any;
      }
      uploadedCount += 1;
      this.emit({ kind: DeployInternalEvent.UPLOAD_PROGRESS, uploaded: uploadedCount, total: missingList.length });
    }
    if (this.cancelRequested) return { ok: false, message: "canceled" } as any;
    this.emit({ kind: DeployInternalEvent.UPLOAD_DONE });

    const second = await createReq();
    if (second.ok && (second as any).id) {
      // 再次防御：若此时已经点击了取消，则优先远端取消而不是继续轮询
      try {
        if ((second as any).id) this.currentPollingId = (second as any).id;
      }
      catch (e) {
        // noop
      }
      if (this.cancelRequested) {
        try {
          const teamIdForCancel = await getVercelTeamId();
          await cancelDeployment({ id: (second as any).id, token, teamId: teamIdForCancel });
        }
        catch (e) {
          // noop
        }
        this.emit({ kind: DeployInternalEvent.RESULT_ERROR, message: t(DeployInternalError.CANCELED) });
      }
      else {
        this.startPolling((second as any).id, { token, teamId });
      }
    }
    // 成功后也写入配置，避免首次分支未写入到 projectId
    if (second.ok) {
      try {
        const projectId = (second as any)?.raw?.projectId || (await getVercelProjectId());
        await writeVercelConfig({
          teamId: teamId || undefined,
          projectId,
          projectName: newProjectName,
        });
        const teamObj2 = (second as any)?.raw?.team;
        const teamNameFromSecond = teamObj2?.slug || teamObj2?.name;
        const teamIdFromSecond = teamObj2?.id;
        this.emit({ kind: DeployInternalEvent.DEPLOYMENT_CONTEXT, id: (second as any)?.id, teamId: teamIdFromSecond || teamId, teamName: teamNameFromSecond, projectId, projectName: newProjectName });
      }
      catch (e) {
        console.error("[deploy] VercelDeploymentProvider deployCore error", e);
      }
    }
    return second as DeployResult;
  }

  private mapReadyStateToPhase(readyState?: string): DeploymentPhase | undefined {
    switch ((readyState || "").toUpperCase()) {
      case "QUEUED": return "QUEUED";
      case "BUILDING":
      case "INITIALIZING":
      case "DEPLOYING": return "BUILDING";
      case "READY": return "READY";
      case "ERROR":
      case "CANCELED":
      case "CANCELLED": return "ERROR";
      default: return undefined;
    }
  }
}
