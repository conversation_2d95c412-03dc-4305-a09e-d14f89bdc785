import { SessionTokenUsage, TokenUsageData } from "shared/lib/LocalService/types";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { LoggerManager } from "../../base/logger";
import { LocalService } from "../../core/localService";
import { SqlLite } from "../sql-lite";
import { Api } from "../../base/http-client";
import { TokenUseageShape } from "shared/lib/bridge/protocol";

export class TokenUsageService extends ServiceModule implements TokenUseageShape {
  private readonly loggerScope = "TokenUsageService";
  private readonly TOKEN_USAGE_KEY_PREFIX = "token_usage_session_";

  constructor(ext: ContextManager) {
    super(ext);
    // 监听token使用量数据
    this.getCore(LocalService).onMessage("assistant/agent/tokenUsage", async ({ data }) => {
      try {
        const tokenUsageData = data as TokenUsageData;
        this.logger.info("Received token usage data", this.loggerScope, {
          value: {
            sessionId: tokenUsageData.sessionId,
            modelName: tokenUsageData.modelName,
            totalTokens: tokenUsageData.usage.total_tokens,
            tokenUsageData,
          },
        });

        // 保存token使用量到本地存储
        await this.saveTokenUsage(tokenUsageData);
      }
      catch (error) {
        this.logger.error("Failed to handle token usage data", this.loggerScope, { err: error });
      }
    });
  }

  /**
   * 保存Token使用量数据（累加到session总量）
   */
  async saveTokenUsage(tokenUsageData: TokenUsageData): Promise<void> {
    try {
      const sqlLite = this.getService(SqlLite);
      await sqlLite.initiation;

      if (!sqlLite.kwaipilotKV) {
        this.logger.warn("KwaipilotKV not available, skipping token usage save", this.loggerScope);
        return;
      }

      const { usage, sessionId } = tokenUsageData;
      const key = this.getSessionKey(sessionId);

      // 获取现有的session使用量
      const existingUsage = await this.$getSessionTokenUsage(sessionId);
      let maxLength = existingUsage?.tokensLimit || 0;
      if (!existingUsage || existingUsage.modelName !== tokenUsageData.modelName) {
        maxLength = Number((await this.getBase(Api).getComposerModelList()).find(model => model.modelType === tokenUsageData.modelName)?.maxLength) || 0;
      }

      // 计算新的累计使用量
      const newUsage: SessionTokenUsage = {
        sessionId,
        totalTokens: usage.total_tokens || existingUsage?.totalTokens || 0,
        promptTokens: usage.prompt_tokens || existingUsage?.promptTokens || 0,
        completionTokens: usage.completion_tokens || existingUsage?.completionTokens || 0,
        cachedCreationInputTokens: usage.cached_creation_input_tokens || existingUsage?.cachedCreationInputTokens || 0,
        cachedReadInputTokens: usage.cached_read_input_tokens || existingUsage?.cachedReadInputTokens || 0,
        requestCount: (existingUsage?.requestCount || 0) + 1,
        lastUpdateTime: Date.now(),
        modelName: tokenUsageData.modelName,

        tokensLimit: maxLength,
      };

      // 保存到KV存储
      await sqlLite.kwaipilotKV.set(key, newUsage);

      this.logger.info("Token usage updated for session", this.loggerScope, {
        value: {
          sessionId,
          totalTokens: newUsage.totalTokens,
          requestCount: newUsage.requestCount,
        },
      });
    }
    catch (error) {
      this.logger.error("Failed to save token usage", this.loggerScope, { err: error });
    }
  }

  /**
   * 获取会话Token使用量
   */
  async $getSessionTokenUsage(sessionId: string): Promise<SessionTokenUsage | null> {
    try {
      const sqlLite = this.getService(SqlLite);
      await sqlLite.initiation;

      if (!sqlLite.kwaipilotKV) {
        this.logger.warn("KwaipilotKV not available", this.loggerScope);
        return null;
      }

      const key = this.getSessionKey(sessionId);
      const usage = await sqlLite.kwaipilotKV.getObject<SessionTokenUsage>(key);

      if (usage && usage.tokensLimit === 0) {
        const maxLength = Number((await this.getBase(Api).getComposerModelList()).find(model => model.modelType === usage?.modelName)?.maxLength) || 2000000;
        usage.tokensLimit = maxLength;
      }

      this.logger.info("Fetched token usage for session", this.loggerScope, { value: usage });

      return usage || null;
    }
    catch (error) {
      this.logger.error("Failed to get session token usage", this.loggerScope, { err: error });
      return null;
    }
  }

  /**
   * 删除会话Token使用量
   */
  async $deleteSessionTokenUsage(sessionId?: string): Promise<void> {
    try {
      const sqlLite = this.getService(SqlLite);
      await sqlLite.initiation;

      if (!sqlLite.kwaipilotKV) {
        this.logger.warn("KwaipilotKV not available", this.loggerScope);
        return;
      }

      if (sessionId) {
        // 删除指定会话的token使用量
        const key = this.getSessionKey(sessionId);
        await sqlLite.kwaipilotKV.delete(key);

        this.logger.info("Token usage deleted for session", this.loggerScope, {
          value: { sessionId },
        });
      }
      else {
        // 删除所有token使用量记录
        await this.deleteAllTokenUsage();

        this.logger.info("All token usage records deleted", this.loggerScope);
      }
    }
    catch (error) {
      this.logger.error("Failed to delete session token usage", this.loggerScope, { err: error });
    }
  }

  /**
   * 获取所有会话的Token使用量（用于统计）
   */
  async getAllSessionsTokenUsage(): Promise<SessionTokenUsage[]> {
    try {
      const sqlLite = this.getService(SqlLite);
      await sqlLite.initiation;

      if (!sqlLite.kwaipilotKV) {
        this.logger.warn("KwaipilotKV not available", this.loggerScope);
        return [];
      }

      // 注意：这个方法需要遍历所有key，在实际使用中可能需要优化
      // 这里只是一个简单的实现示例
      const allUsages: SessionTokenUsage[] = [];

      // 由于KwaipilotKV没有提供遍历所有key的方法，这里只能返回空数组
      // 如果需要统计功能，可以考虑维护一个session列表

      return allUsages;
    }
    catch (error) {
      this.logger.error("Failed to get all sessions token usage", this.loggerScope, { err: error });
      return [];
    }
  }

  /**
   * 删除所有Token使用量记录
   */
  private async deleteAllTokenUsage(): Promise<void> {
    try {
      const sqlLite = this.getService(SqlLite);
      await sqlLite.initiation;

      if (!sqlLite.kwaipilotKV) {
        this.logger.warn("KwaipilotKV not available", this.loggerScope);
        return;
      }

      // 获取 SqlLite 服务的 sequelize 实例
      const sequelize = (sqlLite as any).sequelize;

      if (sequelize) {
        // 使用原生 SQL 查询删除所有以前缀开头的记录
        await sequelize.query(
          `DELETE FROM KwaipilotKV WHERE key LIKE ?`,
          {
            replacements: [`${this.TOKEN_USAGE_KEY_PREFIX}%`],
            type: sequelize.QueryTypes.DELETE,
          },
        );

        this.logger.info(`Deleted token usage records`, this.loggerScope, {
          value: { prefix: this.TOKEN_USAGE_KEY_PREFIX },
        });
      }
      else {
        this.logger.warn("Sequelize instance not found", this.loggerScope);
      }
    }
    catch (error) {
      this.logger.error("Failed to delete all token usage records", this.loggerScope, { err: error });
      throw error;
    }
  }

  /**
   * 生成session的存储key
   */
  private getSessionKey(sessionId: string): string {
    return `${this.TOKEN_USAGE_KEY_PREFIX}${sessionId}`;
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }
}
