import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import { t } from "i18next";
import { ComposerSessionStorageService, PersistedComposerSessionData } from "./ComposerSessionStorageService";
import { ApiConversationHistoryStorageService } from "./ApiConversationHistoryStorageService";
import { MessageParam } from "shared/lib/agent/types_copied_from_agent";
import { ComposerHistoryStorageService } from "./ComposerHistoryStorageService";
import { APP_NAME } from "shared/lib/const";

interface ChatSessionData {
  sessionId: string;
  apiConversationHistory: MessageParam[];
  session: PersistedComposerSessionData;
}

export class ChatImportExportService extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    vscode.commands.registerCommand(`${APP_NAME}.exportSession`, async () => {
      const sessionId = await vscode.window.showInputBox({
        placeHolder: t("kwaipilot.exportSession.placeholder"),
      });

      if (!sessionId) {
        return;
      }

      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const apiConversationHistoryStorageService = this.getService(ApiConversationHistoryStorageService);
      const [session, apiConversationHistory] = await Promise.all([
        composerSessionStorageService.getComposerSessionData(sessionId),
        apiConversationHistoryStorageService.getApiConversationHistory(sessionId),
      ]);
      if (!session) {
        vscode.window.showInformationMessage(`Session not found`);
        return;
      }
      if (!apiConversationHistory) {
        vscode.window.showInformationMessage(`Api conversation history not found`);
        return;
      }

      const filename = `${APP_NAME}-session-export-${sessionId}.json`;
      const workspaceFolder = vscode.workspace.workspaceFolders?.[0]?.uri;
      if (!workspaceFolder) {
        vscode.window.showInformationMessage(`Workspace folder not found`);
        return;
      }
      const filepath = vscode.Uri.joinPath(workspaceFolder, filename); ;

      const structuredData: ChatSessionData = {
        sessionId,
        apiConversationHistory,
        session,
      };
      await vscode.workspace.fs.writeFile(filepath, Buffer.from(JSON.stringify(structuredData, null, 2)));

      await vscode.window.showTextDocument(filepath);
      vscode.window.showInformationMessage(`Export session:${sessionId} done`);
    });
    vscode.commands.registerCommand(`${APP_NAME}.importSession`, async () => {
      const file = await vscode.window.showOpenDialog({
        canSelectMany: false,
        filters: {
          Json: ["json"],
        },
      }).then(res => res?.[0]);
      if (!file) {
        return;
      }
      const data = await vscode.workspace.fs.readFile(file);
      const structuredData = JSON.parse(data.toString()) as ChatSessionData;
      for (const key of Object.keys(structuredData)) {
        if (!(key in structuredData)) {
          vscode.window.showInformationMessage(`wrong file format, ${key} not found`);
          return;
        }
      }
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const apiConversationHistoryStorageService = this.getService(ApiConversationHistoryStorageService);
      const composerHistoryStorageService = this.getService(ComposerHistoryStorageService);

      await composerSessionStorageService.setComposerSessionData(structuredData.sessionId, {
        ...structuredData.session,
        workspaceUri: ext.context.storageUri?.toString() || "",
      });
      await apiConversationHistoryStorageService.setApiConversationHistory(structuredData.sessionId, structuredData.apiConversationHistory);

      await composerHistoryStorageService.updateById(structuredData.sessionId, {
        sessionId: structuredData.sessionId,
        name: `Imported ${structuredData.sessionId}`,
        workspaceUri: ext.context.storageUri?.toString() || "",
        agentMode: structuredData.session.agentMode,
      });
      vscode.window.showInformationMessage(`Import session:${structuredData.sessionId} done`);
    });
  }
}
