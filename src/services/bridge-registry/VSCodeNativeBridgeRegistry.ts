import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { ExtensionContext } from "shared/lib/bridge/protocol";
import { ComposerService } from "../composer";
import { GlobalStateManager } from "../../base/state-manager";
import { IndexFileService } from "../index-file";
import { MCPService } from "../mcp";
import { RulesService } from "../rules";
import { DeveloperService } from "../developer";
import { SettingPanelModule } from "../../core/setting-panel";
import { Webview } from "@webview";
import { ToLocalService } from "../to-loacl-message";
import { WebloggerManager } from "../../base/weblogger";
import { MiscService } from "../misc";
import { TerminalManager } from "../terminal";
import { WikiService } from "../wiki";
import { TokenUsageService } from "../token-usage";

export class BridgeRegistry extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    const settingPanel = this.getCore(SettingPanelModule);
    const sidebarPanel = this.getBase(Webview);
    // settingPanel需要关系的service
    settingPanel.rpcContext.set(ExtensionContext.ExtensionIndexFile, this.getService(IndexFileService));
    settingPanel.rpcContext.set(ExtensionContext.ExtensionWiki, this.getService(WikiService));
    settingPanel.rpcContext.set(ExtensionContext.ExtensionMCP, this.getService(MCPService));
    settingPanel.rpcContext.set(ExtensionContext.ExtensionRules, this.getService(RulesService));
    settingPanel.rpcContext.set(ExtensionContext.ExtensionWeblogger, this.getBase(WebloggerManager));
    settingPanel.rpcContext.set(ExtensionContext.ExtensionSettings, this.getCore(SettingPanelModule));
    // sidebarPanel需要关系的service
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionSettings, this.getCore(SettingPanelModule));
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionComposer, this.getService(ComposerService));
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionMCP, this.getService(MCPService));
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionConfig, this.getBase(GlobalStateManager));
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionDeveloper, this.getService(DeveloperService));
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionToLoacl, this.getService(ToLocalService));
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionMisc, this.getService(MiscService));
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionTerminal, this.getService(TerminalManager));
    sidebarPanel.rpcContext.set(ExtensionContext.ExtensionWebview, this.getBase(Webview));
    sidebarPanel.rpcContext.set(ExtensionContext.TokenUseage, this.getService(TokenUsageService));
  }
}
