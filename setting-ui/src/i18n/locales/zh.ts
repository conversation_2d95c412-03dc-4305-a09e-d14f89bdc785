import { APP_NAME, APP_NAME_PASCAL_CASE } from "shared/lib/const/index";

export const zh = {
  // 主导航标签
  "nav.basics": "基础",
  "nav.function": "功能",
  "nav.fileIndex": "索引",
  "nav.mcp": "工具",
  "nav.rules": "规则",

  // 基础设置页面
  "basics.general.title": "通用",
  "basics.account.title": "账户",
  "basics.account.settings": "账号设置",
  "basics.account.currentUser": "当前登录账号：",
  "basics.account.notLoggedIn": "未登录",
  "basics.account.logout": "注销",
  "basics.account.login": "登录",
  "basics.configMigration.title": "配置迁移",
  "basics.configMigration.description": `导入 VS Code 或 Cursor 中的所有插件、设置、MCP以及快捷键到 ${APP_NAME_PASCAL_CASE} 中，请注意，导入后将覆盖当前配置，且不可恢复。`,
  "basics.ideSetting.title": "IDE设置",
  "basics.ideSetting.description": "前往文本、工作台、窗口等IDE基础设置",
  "basics.ideSetting.button": "去设置",
  "basics.shortcutSetting.title": "快捷键设置",
  "basics.shortcutSetting.description": "前往IDE的快捷键自定义设置",
  "basics.shortcutSetting.button": "去设置",
  "basics.other.title": "其他",
  "basics.proxyUrl.title": "代理URL",
  "basics.proxyUrl.description": "内部调试使用",
  "basics.proxyUrl.prod": "生产环境(默认)",
  "basics.proxyUrl.pre": "预发环境",
  "basics.proxyUrl.test": "测试环境",
  "basics.proxyUrl.idc": "IDC环境",
  "basics.modelType.title": "推理模型",
  "basics.modelType.description": "内部调试使用",
  "basics.about.title": "关于",
  "basics.userAgreement": "用户协议",
  "basics.privacy": "隐私政策",
  "basics.openSource": "开源软件声明",

  // 功能设置页面
  "function.codeCompletion.title": "代码续写",
  "function.codeCompletion.enable.title": "代码续写",
  "function.codeCompletion.enable.description": "基于当前编辑行为智能生成多行代码编辑推荐",
  "function.codeCompletion.commentEnable.title": "注释默认续写",
  "function.codeCompletion.commentEnable.description": "开启后注释内容也会出现续写提示",
  "function.codeCompletion.delay.title": "续写等待时间",
  "function.codeCompletion.delay.description": "在等待时间(ms)结束后才会出现代码续写提示",
  "function.codeCompletion.prediction.title": "启用代码编辑预测",
  "function.codeCompletion.prediction.description": "基于当前编辑行为智能预测下一个改动点并生成多行代码编辑推荐",
  "function.codeBlock.title": "代码块操作",
  "function.codeBlock.enable.title": "代码块操作",
  "function.codeBlock.enable.description": "启用代码块操作、函数注释、行间注释等功能展示",
  "function.inlineTip.title": "智能体模式",
  "function.agentPreference.title": "智能体模式偏好",
  "function.inlineTip.ide.title": "智能对话",
  "function.agentPreference.ide.title": "智能对话偏好",
  "function.defaultChatMode.title": "默认模式",
  "function.defaultChatMode.description": "新建会话时默认选中的模式",
  "function.defaultChatMode.agent": "智能体",
  "function.defaultChatMode.ask": "问答",
  "function.defaultChatMode.edit": "编辑",
  "function.defaultChatMode.readonly": "只读",
  "function.defaultChatMode.follow": "跟随上次选择",
  "function.agentPreference.intelligent": "智能优先",
  "function.agentPreference.speed": "速度优先",
  "function.agentPreference.intelligentDesc": "更强的推理和上下文理解能力，支持多模态输入，适合复杂或多步编辑的开发任务",
  "function.agentPreference.speedDesc": "更快的响应速度，仅支持文本输入，适合快速验证或频繁迭代的开发任务",
  "function.diagnosticsCheck.title": "自动检测 Lint 问题",
  "function.diagnosticsCheck.description": "开启后，在智能体模式下将检测生成代码的 Lint 错误和一键修复建议",
  "function.diagnosticsCheck.ide.description": "开启后，在智能对话模式下将检测生成代码的 Lint 错误和一键修复建议",

  "function.figma.title": "Figma配置",
  "function.figma.delete": "删除",

  // 自动运行模式
  "autoRun.title": "自动运行模式",
  "autoRun.description": "开启后智能体将无需确认自动运行合理的工具，例如命令行执行和 MCP 执行等",
  "autoRun.confirmTitle": "切换为自动运行",
  "autoRun.confirmMessage": "开启后，智能体将自动执行除黑名单外的命令，请注意可能的潜在安全风险",
  "autoRun.confirmButton": "确认",
  "autoRun.cancelButton": "取消",
  "autoRun.blacklist.title": "命令黑名单",
  "autoRun.blacklist.description": "命令前缀在黑名单内的命令，将不会自动执行，始终会询问用户",
  "autoRun.blacklist.placeholder": "请输入名称",
  "autoRun.blacklist.addButton": "添加",
  "autoRun.mcp.title": "MCP 自动运行",
  "autoRun.mcp.description": "启用后，将允许智能体自动运行 MCP",

  // 代码索引页面
  "fileIndex.title": "代码索引",
  "fileIndex.description": "构建仓库代码全局索引，会话时将自动检索问题相关上下文，提升代码问答准确性",
  "fileIndex.autoEnable.title": "自动构建索引",
  "fileIndex.autoEnable.description": "开启后新开项目将自动开始构建",
  "fileIndex.ignoreFolder.title": "忽略/指定文件目录",
  "fileIndex.ignoreFolder.description": "配置构建代码索引时忽略或指定的文件目录",
  "fileIndex.ignoreFolder.button": "前往配置",
  "fileIndex.maxSpace.title": "最大索引空间大小(单位GB)",
  "fileIndex.startBuild": "开始构建",
  "fileIndex.cancelBuild": "取消构建",
  "fileIndex.rebuild": "重新构建",
  "fileIndex.deleteIndex": "删除索引",
  "fileIndex.building": "正在构建",
  "fileIndex.buildSuccess": "构建成功",
  "fileIndex.notRepo": "不是git仓库",
  "fileIndex.paused": "构建暂停",
  "fileIndex.notBuilt": "当前未构建索引",
  "fileIndex.wiki.title": "项目 Wiki",
  "fileIndex.wiki.description": "生成项目摘要，包含系统架构、模块及依赖关系等，进一步增强代码上下文理解",
  "fileIndex.wiki.startBuild": "开始构建",
  "fileIndex.wiki.cancelBuild": "取消构建",
  "fileIndex.wiki.rebuild": "重新构建",
  "fileIndex.wiki.deleteWiki": "删除 Wiki",
  "fileIndex.wiki.building": "构建 Wiki 中...",
  "fileIndex.wiki.buildSuccess": "构建成功",
  "fileIndex.wiki.openWiki": "打开 Wiki",

  // MCP 页面
  "mcp.title": "MCP 管理",
  "mcp.tool": "MCP 工具",
  "mcp.systemTool": "系统工具",
  "mcp.servers": "MCP Servers",
  "mcp.description": `模型上下文协议 (Model Context Protocol, MCP) 是一种为 ${APP_NAME_PASCAL_CASE} 助理提供工具和功能来扩展助理的能力`,
  "mcp.descriptionWithMarket": `模型上下文协议 (Model Context Protocol, MCP) 是一种为 ${APP_NAME_PASCAL_CASE} 智能体模式提供工具和功能来扩展智能体能力。`,
  "mcp.descriptionWithMarketInternal": "详细说明及环境准备请见",
  "mcp.market": "MCP市场",
  "mcp.manualConfig": "手动配置",
  "mcp.quickAdd": "快速添加",
  "mcp.noServers": "暂未添加MCP Servers，请添加",
  "mcp.tooManyTools": "已启用 {{0}} 个工具，超40个工具会降低性能且部分模型不支持，系统将自动裁剪多余工具。",
  "mcp.status.connected": "可使用",
  "mcp.status.connecting": "准备中",
  "mcp.status.disconnected": "不可使用",
  "mcp.tooltip.refresh": "更新",
  "mcp.tooltip.edit": "编辑",
  "mcp.tooltip.delete": "删除",
  "mcp.tools": "Tools:",
  "mcp.toolParams": "参数",
  "mcp.required": "*",

  "mcp.system.title": "系统工具",
  "mcp.system.figma.description": "链接 Figma 以读取设计稿信息并生成代码",
  "mcp.system.figma.link": "链接",
  "mcp.system.figma.unlink": "取消链接",
  "mcp.system.figma.linkText": "您的 Figma 账号 {{0}} 已链接",
  "mcp.system.figma.unlinkTip": "您的 Figma 账号已取消链接",
  "mcp.system.figma.unlinkConfirmTitle": "确认取消链接？",
  "mcp.system.figma.unlinkConfirmMessage": "取消链接后，您将无法使用 Figma 相关功能。确定要取消链接吗？",
  "mcp.system.vercel.description": "链接 Vercel 以部署项目",
  "mcp.system.vercel.link": "链接",
  "mcp.system.vercel.unlink": "取消链接",
  "mcp.system.vercel.linkText": "您的 Vercel 账号已链接",
  "mcp.system.vercel.unlinkTip": "您的 Vercel 账号已取消链接",
  "mcp.system.vercel.unlinkConfirmTitle": "确认取消链接？",
  "mcp.system.vercel.unlinkConfirmMessage": "取消链接后，您将无法使用 Vercel 相关功能。确定要取消链接吗？",
  "vercelOAuthPageOpened": "Vercel OAuth页面已打开，请在浏览器中完成授权。",
  "vercelOAuthFailedToOpenPage": "无法打开Vercel OAuth页面，请检查网络连接。",

  "mcp.system.browserAction.title": "浏览器",
  "mcp.system.browserAction.description": "智能体执行过程中自主运行内置浏览器，以获取网页内容或操作网页",

  // 规则配置页面
  "rules.title": "规则",
  "rules.personalRule.title": "个人规则",
  "rules.personalRule.description": "在此文件中配置用户个人规则后，所有对话均遵循设定规则，且跨项目切换时持续生效。",
  "rules.personalRule.button": "打开",
  "rules.projectRule.title": "项目规则",
  "rules.projectRule.description": `配置项目内使用的规则，仅在当前项目的会话中生效，可在当前项目的.${APP_NAME}/rules 文件夹下查看当前项目内的所有规则。`,
  "rules.projectRule.button": "打开",

  // 导入按钮组件
  "import.button": "导入{{0}} 配置",
  "import.loading": "导入中...",
  "import.confirmTitle": "确认导入 {{0}} 配置吗？",
  "import.confirmMessage": `导入后将覆盖 ${APP_NAME_PASCAL_CASE} 当前配置，且不可恢复`,
  "import.confirm": "确认",
  "import.cancel": "取消",

  // 通用按钮和操作
  "common.confirm": "确认",
  "common.cancel": "取消",
  "common.add": "添加",
  "common.delete": "删除",
  "common.edit": "编辑",
  "common.save": "保存",
  "common.open": "打开",
  "common.close": "关闭",
  "common.refresh": "刷新",
  "common.loading": "加载中...",
  "common.success": "成功",
  "common.error": "错误",
  "common.warning": "警告",

  // MCP 快捷添加按钮
  "mcp.quickAdd.button": "快捷配置",
  "mcp.quickAdd.placeholder": "请输入MCP server名称",
  "mcp.quickAdd.noData": "暂无数据",
  "mcp.quickAdd.featured": "精选",
  "mcp.quickAdd.kuaishou": "快手",
  "mcp.quickAdd.added": "已添加",
  "mcp.quickAdd.add": "添加",
  "mcp.quickAdd.otherServers": "其他 MCP Server 可前往",
  "mcp.quickAdd.marketLink": "MCP市场",
  "mcp.quickAdd.manualAdd": "进行手动添加",

  // MCP 错误信息
  "mcp.error.fetchServersFailed": "获取MCP服务器列表失败",
  "mcp.featureIntroduction": "MCP 功能介绍",

  // MCP 快速添加对话框
  "mcp.quickAdd.title": "添加 MCP Server",
  "mcp.quickAdd.description": "您将添加 {{0}} ，此MCP Server无需提供额外配置信息，点击\"确认\"完成添加。",
  "mcp.quickAdd.installFailed": "安装失败",

  // 语言设置
  "basics.language.title": "语言",
  "basics.language.description": `配置${APP_NAME_PASCAL_CASE}使用的文本语言`,

  "basics.exportLog": "日志导出",
  "basics.copyLogUrl": "复制日志链接",

  // 数据导入
  "basics.dataImport.title": "数据导入",
  "basics.dataImport.description": "选择 SQLite 数据库文件进行数据导入（生产环境调试用）",
  "basics.dataImport.button": "选择数据库文件",
  "basics.dataImport.currentPath": "当前数据库路径：",
  "basics.dataImport.defaultPath": "默认路径",
  "basics.dataImport.clearPath": "清除路径",
};
