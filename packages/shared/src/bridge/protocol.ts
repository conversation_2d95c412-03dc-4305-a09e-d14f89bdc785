import { ComposerState, InternalWebviewMessage, DiffSet } from "../agent";
import { SupportedModels } from "../agent/supportedModels";
import { DeleteMcpServerParams, McpServer, RestartMcpServerParams, ToggleMcpServerParams, InstallMcpParams, MCPFeaturedServer, FetchMcpDetailByMarketParams } from "../mcp/types";
import { FromIdeProtocol, ResponseBase } from "../LocalService";
import { MentionNodeV2Structure } from "../MentionNodeV2/nodes";
import { createProxyIdentifier } from "./proxyIdentifier";
import { SerializedDiagnostic } from "../misc/diagnostic";
import { ReportKeys } from "../misc/logger";
import { SettingPage } from "../customSettingPanel";
import { SerializedEditorState } from "lexical";
import { BridgeUploadFile, FileData } from "../misc/file";
import { StateReturnType } from "../state-manager/types";
import { PreviewUrl, SessionTokenUsage } from "../LocalService/types";
import { IChatModeType, IAgentMode } from "../business";
import { WorkspaceFolder } from "vscode";
import { URI } from "vscode-uri";
import { WikiState } from "..";
import { DiffLineInfo } from "../misc/blockcode";

export type UriComponents = ReturnType<URI["toJSON"]>;

/**
 * extension 端提供的助理模式相关 api
 */
export interface ExtensionComposerShape {
  $setEditingMessageTs(ts: number | undefined): void;

  /**
   * 保存当前的 context items
   * @param contextItems
   */
  $saveContextItems(contextItems: MentionNodeV2Structure[]): void;

  /**
   * 恢复到指定的检查点
   * @param restoreCommitHash 检查点哈希值
   */
  $restoreCheckpoint(payload: {
    humanMessageTs: number;
    restoreCommitHash: string;
    updateStateImmediately: boolean;
  }): void;

  /**
   * 回退历史(不恢复检查点)
   * @param payload
   */
  $revertHistory(payload: {
    humanMessageTs: number;
    updateStateImmediately: boolean;
  }): void;
  /**
   * 获取diff
   * @param lhsHash 旧的检查点
   * @param rhsHash 新的检查点
   */
  $getDiffSet(lhsHash: string, rhsHash?: string): Promise<DiffSet[]>;

  /**
   * webview-ui 发起的 助理模式更新消息，例如用户输入、命令执行，具体查看
   * @see {InternalWebviewMessage} 了解更多信息。
   */
  $postMessageToComposerEngine(payload: InternalWebviewMessage): void;

  /**
   * 设置当前对话的模型
   * @param model @see {SupportedModels}
   */
  $setCurrentModel(model: SupportedModels): void;

  /**
   * 设置当前对话的模式
   * @param mode "ask" | "agent"
   */
  $setCurrentMode(mode: IChatModeType): Promise<void>;
  /**
   * 设置当前对话的类型
   * @param type "jam" | "duet"
   */
  $setAgentMode(type: IAgentMode): Promise<void>;
  /**
   * 获取当前对话的类型
   * @returns "jam" | "duet"
   */
  $getAgentMode(): Promise<IAgentMode>;

  /**
   * 获取当前对话的类型
   * @returns "ask" | "agent"
   */
  $getChatMode(): Promise<IChatModeType>;

  /**
   * 获取当前对话的类型和 agent 模式
   * @returns { chatMode: IChatModeType; agentMode: IAgentMode }
   */
  $getCurrentChatData(): Promise<{ chatMode: IChatModeType; agentMode: IAgentMode }>;

  /**
   * 获取置当前对话的模式
   * @param mode "ask" | "agent"
   */
  $getDefaultMode(): Promise<IChatModeType>;

  /** 切换 session 时， 切换历史记录 */
  $showTaskWithId(taskId: string): void;

  /**
   * 定位到 文件或 selection 等知识所属的文档
   * @param uri 文件 uri
   * @param range 范围
   */
  $locateMentionNodeV2(node: MentionNodeV2Structure): void;

  $openImageInEditor(imgString: string): void;

  $locateByPath(absolutePath: string): void;

  $locateDiagnostic(uri: string, diagnostic: SerializedDiagnostic): void;

  /**
   * 唤起添加rules的流程
   */
  $addRuleFile(): void;

  /**
   * 打开设置页面并定位到 助理检测 lint 配置的章节
   */
  $openDiagnosticSetting(): void;

  /**
   * vscode.workspace.workspaceFile
   */
  $getWorkspaceFile(): string | undefined;
  /**
   * 设置当前工作区的 active session id
   * @param sessionId
   */
  $setActiveSessionId(sessionId: string): void;
  /**
   * 获取当前工作区的 active session id
   */
  $getActiveSessionId(): string;

  /**
 * 将用户输入的富文本结构体（editorState）转换为适合 LLM 的，带上下文的文本
 * @param editorState
 * @returns
 */
  $editorStateToContextualTextForLlm(editorState: SerializedEditorState, cwd: string, contextItems: MentionNodeV2Structure[]): Promise<string>;
  /** 在 IDE 内开启预览功能 */
  $openIDEPreview(path: string): Promise<ResponseBase<PreviewUrl>>;
  /** 在独立浏览器中开启预览功能 */
  $openBrowserPreview(path: string): Promise<ResponseBase<PreviewUrl>>;
  $addFileToContext(uri: string): void;
  $setMcpCanCommandProceededByUserStatus(status: boolean): void;
  $getHistoryHumainMessage(direction: "pre" | "next"): Promise<{
    text?: string;
    editorState?: SerializedEditorState;
    contextItems?: MentionNodeV2Structure[];
  }>;
}

/**
 * extension weblogger埋点
 */
export interface ExtensionWebloggerShape {
  $reportUserAction<T extends keyof ReportKeys>(param: {
    key: T;
    type?: string;
    chatId?: string;
    sessionId?: string;
    content?: string;
    operator?: string;
    subType?: string;
    applyId?: string;
    agentMode?: IAgentMode;
  }): void;
}

/**
 * extension 索引构建
 */
export interface ExtensionIndexFileShape {
  $startBuildIndex(): void;
  $deleteIndex(): void;
  $stopIndex(): void;
  $rebuildIndex(): void;
  $openIndexIgnore(): void;
  $setMaxSpaceSize(value: number): void;
  $getMaxSpaceSize(): number;
  $openSystemSettings(): void;
  $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]>;
}

/**
 * extension 项目 wiki 构建
 */
export interface ExtensionWikiShape {
  $startBuildWiki(): void;
  $deleteWiki(): void;
  $rebuildWiki(): void;
  $openWiki(): Promise<void>;
  $cancelBuildWiki(): void;
  $getWikiState(): WikiState;
  $checkWikiStatus(): Promise<void>;
}
/**
 * extension MCP
 */
export interface ExtensionMCPShape {
  $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]>;
  /** 获取完整 mcp 配置文件路径 */
  $getSettingsPath(): Promise<ResponseBase<string>>;
  /** 获取全量 mcp servers 列表 */
  $getAllMcpServers(): Promise<ResponseBase<{ mcpServers: McpServer[]; isError: boolean }>>;
  /** 启、停用单个 mcp server */
  $toggleMcpServer(params: ToggleMcpServerParams): Promise<ResponseBase<void>>;
  /** 重启单个 mcp server */
  $restartMcpServer(params: RestartMcpServerParams): Promise<ResponseBase<void>>;
  /** 删除单个 mcp server */
  $deleteMcpServer(params: DeleteMcpServerParams): Promise<ResponseBase<void>>;
  /** 获取 mcp 推荐服务器列表 */
  $getMcpFeaturedServers(): Promise<ResponseBase<{ records: MCPFeaturedServer[] }>>;
  /** 安装 mcp server */
  $installMcpServer(params: InstallMcpParams): Promise<ResponseBase<boolean>>;
  /** 获取 mcp 市场详情 */
  $fetchMcpDetailByMarket(params: FetchMcpDetailByMarketParams): Promise<ResponseBase<MCPFeaturedServer>>;
}

/**
 * extension UI Preview
 */
export interface ExtensionUIPreviewShape {
  $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]>;
  /** 在 IDE 内开启预览功能 */
  $openIDEPreview(path: string): Promise<ResponseBase<string>>;
  /** 在独立浏览器中开启预览功能 */
  $openIDEPreview(path: string): Promise<ResponseBase<string>>;
}

/**
 * extension Rules
 */
export interface ExtensionRulesShape {
  $openUserRule(): void;
  $openProjectRules(): void;
}

/**
 * Config 端提供的 api
 */
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface ExtensionConfigShape {
}

/**
 * 开发者模式相关 api
 */
export interface ExtensionDeveloperShape {
  $reloadWebview(): void;
  $getIsDeveloperMode(): boolean;
  $setIsDeveloperMode(isDeveloperMode: boolean): void;
}
/**
 * webview 直接请求本地包的服务
 */
export interface ExtensionToLoaclShape {
  $parseRules(rules: string[]): Promise<string[]>;
}

/**
 * terminal 相关 api
 */

export interface ExtensionTerminalShape {
  $getPtyProcessID(): number;
  $writeToTerminal(pid: number, data: string): void;
  $showTerminal(pid?: number): void;
  // 新建会话时将所有非background的进程干掉
  $disposeTerminalIfNotIsBackground(): number[];
  $skipCommand(): void;
  $setTerminalCanCommandProceededByUserStatus(status: boolean): void;
}

/**
 * 设置页相关 api
 */
export interface ExtensionSettingsShape {
  $openSettings(activePage?: SettingPage): void;

  $updateSetting(key: keyof StateReturnType["config"], value: StateReturnType["config"][typeof key]): void;

  $getSettings(): StateReturnType["config"];
  /**
   * 登录
   * @param host ide | plugin 默认是 plugin
   */
  $login(host?: "ide" | "plugin"): void;
  /**
   * 登出
   * @param host ide | plugin 默认是 plugin
   */
  $logout(host?: "ide" | "plugin"): void;

  $importSettings(productName: string): void;
  $openIdeShortcutSettings(): void;
  $openIdeUserSettings(): void;
  $changeKwaipilotLang(lang: string): void;

  $generateLogUrl(isAutoCopyed?: boolean): Promise<string | void>;
  $generateHistoryLogUrl(start: number, end: number, sessionId: string): Promise<string | void>;
  $copyToClipboard(text: string, tip?: string): Promise<void>;

  /**
   * 选择 SQLite 数据库文件用于数据导入
   */
  $selectSqliteDatabase(): Promise<string | void>;

  /**
   * 显示重启应用提示
   */
  $showReloadWindowPrompt(message: string): Promise<void>;
}

/** webview页面相关api */
export interface ExtensionWebviewShap {
  /** 插件层更具 webview类型判断还原到 siderbar 还是打开到 编辑区新tab */
  $moveFullMode(newWindow: boolean): void;
  $moveToSidebar(): void;

  /** 定位到当前可用的问答视图 */
  $reveal(): void;

  /**
   * 获取和设置需要还原的路由
   */
  $setRestoreRoute(path: string): void;
  $getRestoreRoute(): string;
}

export interface TokenUseageShape {
  $getSessionTokenUsage(sessionId: string): Promise<SessionTokenUsage | null>;
  $deleteSessionTokenUsage(sessionId?: string): Promise<void>;
}

export interface FileSearchMatch {
  start: number;
  end: number;
}

export interface SearchResultItem {
  uri: UriComponents;
  label: string;
  highlights: {
    label: FileSearchMatch[];
  };
  relativePath: string;
}

export interface FileSearchResult {
  results: SearchResultItem[];
}

export interface VsWorkspaceStaticState {
  workspaceFolders: WorkspaceFolder[];
}

export interface VsContextStaticState {
  storageUri: URI | undefined;
}

export interface WorkspaceStaticState {
  workspace: VsWorkspaceStaticState;
  context: VsContextStaticState;
}

/**
 * 一些量级比较小或暂时不清楚归属的杂项 api
 */
export interface ExtensionMiscShape {
  $uploadFile(): Promise<BridgeUploadFile[]>;
  $uploadImage(limit: number): Promise<BridgeUploadFile[]>;
  $uploadImageDirectly(files: FileData[], limit: number): Promise<BridgeUploadFile[]>;
  /**
   * 在工作区搜索文件，用于输入框知识搜索
   * @param query
   */
  $doFileSearch(query: string): Promise<FileSearchResult>;
  /**
   * 在工作区搜索目录，用于输入框知识搜索
   * @param query
   */
  $doDirSearch(query: string): Promise<FileSearchResult>;

  /**
   * 获取工作区静态状态
   */
  $getWorkspaceStaticState(): Promise<WorkspaceStaticState>;

  /**
   * 获取知识搜索默认展示的文件
   *
   * 同旧版 getCodeSearchDefaultFiles
   */
  $getMentionDefaultFiles(): Promise<{ uri: UriComponents; relativePath: string }[]>;

  /**
   * 获取知识搜索默认展示的目录
   *
   * 同旧版 getCodeSearchDefaultDirs
   */
  $getMentionDefaultDirectories(): Promise<{ uri: UriComponents; relativePath: string }[]>;
}
/**
 * webview 端提供的助理模式相关 api
 */
export interface WebviewComposerShape {
  $postComposerStateUpdate(state: ComposerState): void;
  /**
   * 添加到上下文 如果不传，则只是定位到助理页面
   * @param node
   * @param position 插入位置，context 表示插入到上下文，cursor 表示插入到光标位置
   */
  $addToComposerContext(node: MentionNodeV2Structure | null, position?: "context" | "cursor"): void;
  $triggerShortcut(command: "newChat" | "runTerminalCommand" | "skipTerminalCommand" | "switchMode" | "runMcp" | "skipMcp"): void;

  // 是否已存在上下节点
  $hasContextItem(item: MentionNodeV2Structure): boolean;

  // EditFile相关
  $updateDiffLineInfo(id: string, info: DiffLineInfo): void;
}

export interface WebviewTerminalShape {
  $write(pid: number, data: string): boolean;
  $getTerminalContent(pid: number, linenumber: number): string;
  $getCurrentLinenumber(pid: number): number;
  // 使用最新的pid去渲染一个terminal
  $renderUiTerminal(pid: number): void;
  $disposeUITerminal(pid: number): void;
}

/**
 * Extension 端提供的 api
 *
 * 注意：新增 api 时，需要同步更新 [bridge-registry](../../../../src/services/bridge-registry/index.ts) 中的注册
 */
export const ExtensionContext = {
  ExtensionComposer: createProxyIdentifier<ExtensionComposerShape>("ExtensionComposer"),
  ExtensionConfig: createProxyIdentifier<ExtensionConfigShape>("ExtensionConfig"),
  ExtensionIndexFile: createProxyIdentifier<ExtensionIndexFileShape>("ExtensionIndexFile"),
  ExtensionWiki: createProxyIdentifier<ExtensionWikiShape>("ExtensionWiki"),
  ExtensionMCP: createProxyIdentifier<ExtensionMCPShape>("ExtensionMCP"),
  ExtensionRules: createProxyIdentifier<ExtensionRulesShape>("ExtensionRules"),
  ExtensionDeveloper: createProxyIdentifier<ExtensionDeveloperShape>("ExtensionDeveloper"),
  ExtensionSettings: createProxyIdentifier<ExtensionSettingsShape>("ExtensionSettings"),
  ExtensionToLoacl: createProxyIdentifier<ExtensionToLoaclShape>("ExtensionToLoacl"),
  ExtensionWeblogger: createProxyIdentifier<ExtensionWebloggerShape>("ExtensionWeblogger"),
  ExtensionMisc: createProxyIdentifier<ExtensionMiscShape>("ExtensionMisc"),
  ExtensionTerminal: createProxyIdentifier<ExtensionTerminalShape>("ExtensionTerminal"),
  ExtensionWebview: createProxyIdentifier<ExtensionWebviewShap>("ExtensionWebview"),
  TokenUseage: createProxyIdentifier<TokenUseageShape>("TokenUseage"),
};

export const WebviewContext = {
  WebviewComposer: createProxyIdentifier<WebviewComposerShape>("WebviewComposer"),
  WebviewTerminal: createProxyIdentifier<WebviewTerminalShape>("WebviewTerminal"),
};
