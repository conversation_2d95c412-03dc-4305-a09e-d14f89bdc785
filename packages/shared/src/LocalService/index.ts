import { EditFileRequest, EditFileResponse, ExecuteCommandResponse, LocalMessage, MessageParam, WebviewMessage, DiffSet, CommandStatusCheckResponse, EnvironmentV2, ReplaceInFileRequest, LocalMessageTextDelta } from "../agent";
import { DeleteMcpServerParams, FetchMcpDetailByMarketParams, InstallMcpParams, MCPFeaturedServer, McpServer, McpServerChangeEventDetail, RepoIndexParam, RestartMcpServerParams, ToggleMcpServerParams, McpConfigChangeNotification } from "../mcp/types";
import { WikiRes } from "../business";
import { DeployServerInfo, PreviewUrl, TokenUsageData, type IdeSettings } from "./types";

export type ResponseBase<T> = {
  status: "ok" | "failed";
  message?: string;
  data?: T;
  code?: number; // 0: 正确，其他code：就是错误码
};
type ChatHistory = {
  role: "user" | "assistant";
  content: string;
};
export type SearchSearchParams = {
  query: string;
  chatHistory: ChatHistory[];
  topK?: number;
  targetDirectory?: string[];
};
export enum RepoStatusEnum {
  SUCCESS = "0",
  MAX_INDEX_SIZE_EXCEEDED_5K = "1",
  MAX_INDEX_SIZE_EXCEEDED_10K = "2",
  INDEX_FAILED = "9999",
  NOT_GIT_REPO = "11",
}

export type ToIdeFromCoreProtocol = {
  "state/updatePort": [{
    port: number;
    host?: string;
  }, void];
  "state/ideState": [undefined, ResponseBase<null>];
  "config/getIdeSetting": [undefined, ResponseBase<IdeSettings>];
  "index/progress": [
    {
      progress: number;
      total: number;
      done: number;
      filepath: string;
      action: "INDEX_FILE" | "PROCESS_FILE" | "ERROR";
      message?: string;
    },
    undefined,
  ];
  "state/sendNotification": [
    {
      type: "error" | "warning" | "info";
      name: string;
      message: string;
    },
    undefined,
  ];
  "state/ideInfo": [undefined, Promise<ResponseBase<{
    pluginVersion: string;
    version: string;
    platform: IdePlatform;
    repoInfo: {
      git_url: string;
      dir_path: string;
      commit: string;
      branch: string;
    };
    userInfo: {
      name: string;
    };
    versionType: "External" | "Internal";
    proxyUrl: string;
    jwtToken?: string;
    cwd: string;
    device?: IdeDevice;
    language: string;
  }>>];
  "assistant/agent/tokenUsage": [TokenUsageData, void];
  /* 新增 or 更新 一条对话数据 */
  "assistant/agent/message": [LocalMessage, void];
  "assistant/agent/messageList": [LocalMessage[], void];
  "assistant/agent/messageDelta": [LocalMessageTextDelta, void];
  /* 同步对话列表数据api_conversation_history */
  "assistant/agent/apiConversationList": [MessageParam[], void];
  "assistant/agent/environment": [{ includeFileDetails: boolean }, Promise<ResponseBase<string>>];
  "assistant/agent/environmentV2": [undefined, Promise<ResponseBase<EnvironmentV2>>];
  "assistant/agent/executeCommand": [{ command: string; is_background: boolean; ignore_output: boolean }, Promise<ResponseBase<ExecuteCommandResponse>>];
  "assistant/agent/commandStatusCheck": [{ check_duration: number }, Promise<ResponseBase<CommandStatusCheckResponse>>];
  "assistant/agent/editFile": [EditFileRequest, Promise<ResponseBase<EditFileResponse>>];
  "assistant/agent/replaceInFile": [ReplaceInFileRequest, Promise<ResponseBase<EditFileResponse>>];
  "assistant/agent/writeToFile": [{ path: string; diff?: string; content: string; newFile: boolean }, Promise<ResponseBase<EditFileResponse>>];
  "assistant/agent/figmaToken": [void, Promise<ResponseBase<string>>];
  "mcp/mcpServerChange": [McpServerChangeEventDetail, void];
  "mcp/configChangeNotification": [McpConfigChangeNotification, void];
  // UI Preview 选点信息
  "uiPreview/info": [{
    type: "error" | "element" | "image";
    displayName: string;
    data: string; // 当 type 为 element 时为 xml 字符串, 为 image 时为临时文件路径, 为 error 时为错误信息,
    desc?: string[] | string; // 用于 tooltip 展示
    focusInfo?: { // dom元素定位需要的信息
      filePath?: string;
      start?: {
        line?: number;
        column?: number;
      };
    };
  }, void];
  // UI Preview 刷新页面
  "uiPreview/refresh": [undefined, void];
  "wiki/generateProgress": [{
    progress: number;
    message?: string;
  }, undefined];
  // 项目wiki信息
  "wiki/getWikiList": [undefined, ResponseBase<WikiRes | null>];
  // 部署
  "deploy/token": [{
    access_token: string;
    user_id: string;
    team_id: string;
    installation_id: string;
    token_type: string;
  }, void];
};

export type IdePlatform = "xcode" | "vscode" | "jetbrains" | "kwaipilot-ide" | "codeflicker-ide";
export type IdeDevice = "kwaipilot-vscode" | "kwaipilot-ide" | "codeflicker-ide" | "kwaipilot-xcode" | "kwaipilot-intellij";
export type IWiki = {
  wikis: {
    content: string;
    filename: string;
  }[];
  path: string;
  lastUpdated: string;
};
export interface WikiBuildState {
  isBuilding: boolean;
  progress: number;
  message: string;
  startTime?: Date;
}

export interface WikiCheckResult {
  exists: boolean;
  buildState?: WikiBuildState;
}

export type ToCoreFromIdeProtocol = {
  // 健康检查接口
  "state/agentState": [undefined, ResponseBase<null>];
  "state/userLogin": [{ username: string }, ResponseBase<null>];
  "state/jwtToken": [{ jwtToken?: string }, ResponseBase<null>];

  // 索引相关接口
  "index/file": [{ file: string; action: "modify" | "delete" | "create" }, ResponseBase<null>];
  "index/build": [undefined, ResponseBase<boolean>];
  "index/pause": [undefined, ResponseBase<boolean>];
  "index/clearIndex": [undefined, ResponseBase<boolean>];
  "index/repoIndex": [RepoIndexParam | undefined, ResponseBase<boolean>];
  "wiki/getWikiList": [undefined, ResponseBase<IWiki | null>];
  // 获取 wiki 状态，是否存在wiki文件以及构建状态
  "wiki/checkWikiStatus": [undefined, ResponseBase<WikiCheckResult>];
  "wiki/generateWiki": [undefined, ResponseBase<boolean>];
  "wiki/deleteWiki": [undefined, ResponseBase<boolean>];
  "wiki/cancelGenerate": [undefined, ResponseBase<boolean>];

  // MCP 相关接口
  "mcp/getSettingsPath": [undefined, ResponseBase<string>];
  "mcp/getAllMcpServers": [undefined, ResponseBase<{ mcpServers: McpServer[]; isError: boolean }>];
  "mcp/toggleMcpServer": [ToggleMcpServerParams, ResponseBase<void>];
  "mcp/restartMcpServer": [RestartMcpServerParams, ResponseBase<void>];
  "mcp/deleteMcpServer": [DeleteMcpServerParams, ResponseBase<void>];
  "mcp/fetchAvailableMcpListByMarket": [undefined, ResponseBase<{ records: MCPFeaturedServer[] }>];
  "mcp/installMcp": [InstallMcpParams, ResponseBase<boolean>];
  "mcp/fetchMcpDetailByMarket": [FetchMcpDetailByMarketParams, ResponseBase<MCPFeaturedServer>];

  // 状态相关接口
  "state/checkRepoState": [undefined, ResponseBase<{
    id: number;
    repo: string;
    repoPath: string;
    branch: string;
    commitId: string;
    lastUpdateTime: number;
    createTime: number;
    total: number;
    done: number;
    progress: number;
    isBuilding: boolean;
    status: RepoStatusEnum;
    isPaused: boolean;
    message: string;
  } | null>];

  // 搜索相关接口
  "search/search": [
    SearchSearchParams,
    ResponseBase<any>,
  ];

  // 助理模式
  "assistant/agent/local": [WebviewMessage, ResponseBase<null>];
  // 助理模式
  "assistant/agent/getDiffSet": [{ sessionId: string; lhsHash: string; rhsHash?: string }, ResponseBase<DiffSet[]>];
  "rules/getRulesList": [{ rules: string[] }, ResponseBase<string[]>];

  // UI预览相关接口
  "uiPreview/previewBrowser": [{ url: string }, ResponseBase<PreviewUrl>];
  "uiPreview/previewProxy": [{ url: string }, ResponseBase<PreviewUrl>];
  "uiPreview/checkPortActive": [{ url: string }, ResponseBase<boolean>];

  // 部署相关
  "deploy/startServer": [undefined, ResponseBase<DeployServerInfo>];
  "deploy/stopServer": [{ port: number }, ResponseBase<DeployServerInfo>];
};

// IDE
export type ToIdeProtocol = ToIdeFromCoreProtocol;
export type FromIdeProtocol = ToCoreFromIdeProtocol;

// Core
export type ToCoreProtocol = ToCoreFromIdeProtocol;
export type FromCoreProtocol = ToIdeFromCoreProtocol;
